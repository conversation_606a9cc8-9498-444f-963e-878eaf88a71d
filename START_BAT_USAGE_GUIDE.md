# start.bat 使用指南

## 🚀 快速开始

### 启动程序
双击 `start.bat` 文件即可启动Chrome Profile Launcher批处理版本。

### 系统要求
- Windows 10/11 操作系统
- Google Chrome 浏览器
- 无需Python环境或其他依赖

## 📋 主菜单功能

启动后会显示主菜单，包含以下选项：

```
========================================
Chrome Profile Launcher - 主菜单
========================================

检测到 X 个Chrome配置文件: Default Profile 1 Profile 2...

请选择操作:

[1] 必应奖励 - 批量启动并打开Bing奖励
[2] 哔哩搜索 - 执行哔哩搜索流程
[3] 自动模式 - 完整自动化流程
[4] 读取记录 - 读取并对比账号数据
[5] 账号注册 - 打开账号注册页面
[6] 清除缓存 - 清除今日txt文件
[7] 关闭Chrome - 关闭所有Chrome进程
[8] 配置文件选择 - 选择要操作的配置文件
[9] 高级选项 - 更多功能选项
[0] 退出程序
```

## 🔧 功能详解

### [1] 必应奖励
- **功能**: 批量启动所有检测到的Chrome配置文件并打开Bing奖励页面
- **流程**: 
  1. 自动创建今日文件夹
  2. 依次启动每个配置文件
  3. 每个配置文件间延迟2秒
  4. 10秒后自动读取记录
- **适用场景**: 日常Bing奖励签到

### [2] 哔哩搜索
- **功能**: 执行哔哩搜索流程
- **流程**:
  1. 对每个配置文件执行两次哔哩搜索访问
  2. 两次访问间隔2秒
  3. 配置文件间延迟3秒
- **适用场景**: 特定搜索任务

### [3] 自动模式
- **功能**: 完整的自动化流程
- **流程**:
  1. 创建今日文件夹
  2. 对每个配置文件依次访问：
     - 哔哩搜索
     - 自动搜索
     - 必应奖励
  3. 每个步骤间延迟3秒
  4. 配置文件间延迟4秒
  5. 完成后自动读取记录
- **适用场景**: 完整的日常自动化任务

### [4] 读取记录
- **功能**: 读取并显示今日txt文件中的账号数据
- **显示格式**:
```
======================================
账号数据对比 (昨日 vs 今日)
======================================
序号  账号                           天数    状态
======================================
1.   <EMAIL>                  7天     正常
2.   <EMAIL>                  5天     正常
======================================
```
- **适用场景**: 检查账号签到状态

### [5] 账号注册
- **功能**: 批量打开Microsoft账号注册页面
- **流程**: 在每个配置文件中打开注册页面
- **适用场景**: 批量注册新账号

### [6] 清除缓存
- **功能**: 删除今日文件夹中的所有txt文件
- **安全提示**: 会显示删除的文件数量
- **适用场景**: 清理错误记录，重新开始

### [7] 关闭Chrome
- **功能**: 关闭所有正在运行的Chrome进程
- **安全确认**: 需要用户确认操作
- **命令**: 使用 `taskkill /f /im chrome.exe`
- **适用场景**: 批量关闭Chrome窗口

### [8] 配置文件选择
- **功能**: 显示当前检测到的配置文件列表
- **说明**: 当前版本对所有配置文件执行操作
- **建议**: 如需精确选择，使用Electron桌面版

### [9] 高级选项
包含以下子功能：
- **[1] 创建今日文件夹**: 手动创建今日日期文件夹
- **[2] 检测Chrome配置文件**: 重新检测配置文件
- **[3] 显示系统信息**: 显示当前配置和路径信息
- **[4] 测试Chrome启动**: 测试Chrome是否能正常启动
- **[5] 打开RTBS目录**: 在文件管理器中打开RTBS目录

## ⚙️ 配置说明

### 路径配置
- **Chrome路径**: `C:\Program Files\Google\Chrome\Application\chrome.exe`
- **RTBS目录**: `C:\Users\<USER>\Downloads\RTBS`
- **用户数据目录**: `%USERPROFILE%\AppData\Local\Google\Chrome\User Data`

### 时间配置
- **浏览器启动延迟**: 3秒
- **页面加载延迟**: 3秒  
- **进程结束延迟**: 5秒
- **自动读取延迟**: 10秒

### URL配置
- **哔哩搜索**: Bing搜索哔哩哔哩
- **必应奖励**: https://rewards.bing.com
- **自动搜索**: Bing搜索特定关键词
- **账号注册**: Microsoft Live账号注册页面

## 🎨 界面特性

### 颜色显示
- **绿色**: 成功操作
- **黄色**: 警告信息
- **红色**: 错误信息
- **白色**: 普通信息

### 操作流程
1. 选择功能（输入数字）
2. 程序自动执行
3. 显示执行结果
4. 按任意键返回主菜单

## 🔍 故障排除

### 常见问题

**Q: 提示"未找到Chrome浏览器"**
A: 检查Chrome是否安装在默认路径，或手动修改bat文件中的CHROME_PATH变量

**Q: 没有检测到配置文件**
A: 确保Chrome至少运行过一次，或使用高级选项重新检测

**Q: 无法创建文件夹**
A: 检查RTBS_DIR路径是否有写入权限

**Q: Chrome启动失败**
A: 使用高级选项中的"测试Chrome启动"功能进行诊断

### 日志查看
程序运行时会在命令行窗口显示详细的执行日志，包括：
- 配置文件启动状态
- 文件夹创建结果
- 记录读取结果
- 错误信息

## 📝 使用技巧

### 1. 批量操作
- 所有功能都会对检测到的所有配置文件执行操作
- 如需精确控制，建议使用Electron桌面版

### 2. 时间安排
- 建议在网络状况良好时使用
- 自动模式适合在空闲时间运行

### 3. 记录管理
- 定期使用"读取记录"功能检查状态
- 必要时使用"清除缓存"重新开始

### 4. 安全使用
- 关闭Chrome前确保重要工作已保存
- 清除缓存前确认不需要当前记录

## 🔄 与Python版本对比

| 特性 | Batch版本 | Python版本 |
|------|-----------|-------------|
| **启动速度** | 极快 | 中等 |
| **界面** | 命令行菜单 | 图形界面 |
| **配置选择** | 全部配置文件 | 可精确选择 |
| **依赖** | 无 | 需要Python |
| **功能完整性** | 核心功能完整 | 功能最全 |

## 🎯 最佳实践

1. **首次使用**: 先运行"高级选项"→"显示系统信息"检查配置
2. **日常使用**: 使用"必应奖励"或"自动模式"
3. **问题排查**: 使用"高级选项"中的诊断功能
4. **定期维护**: 使用"清除缓存"清理错误记录

start.bat提供了与Python版本功能对等的批处理解决方案，适合追求轻量级和快速部署的用户使用！
