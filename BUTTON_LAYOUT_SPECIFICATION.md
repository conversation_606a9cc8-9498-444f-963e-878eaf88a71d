# 按钮布局规范文档

## 📋 概述

本文档详细说明了Chrome Profile Launcher项目中按钮布局的规范化改进，确保在不同屏幕尺寸下都能提供一致、美观的用户体验。

## 🎯 设计原则

### 1. 统一性
- 所有按钮采用相同的基础样式和尺寸规范
- 统一的颜色系统和交互效果
- 一致的间距和对齐方式

### 2. 响应式设计
- 基于窗口化（非最大化）的默认大小进行布局优化
- 在不同屏幕尺寸下保持3列布局的完整性
- 渐进式的尺寸调整，确保可用性

### 3. 用户体验
- 清晰的视觉层次和功能分组
- 流畅的交互动画和反馈
- 符合现代UI设计趋势

## 🔧 技术规范

### 按钮网格布局
```css
.action-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 8px; /* 统一间距 */
    margin-bottom: 8px;
    padding: 0 8px; /* 左右内边距 */
}
```

### 按钮基础样式
```css
.action-btn {
    height: 38px; /* 标准高度 */
    border: 1px solid #d0d0d0;
    background: #ffffff;
    color: #333333;
    border-radius: 4px; /* 轻微圆角 */
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease; /* 过渡动画 */
}
```

### 交互效果
- **悬停效果**: 上移1px + 阴影 + 颜色变化
- **激活状态**: 重置位移，保持轻微阴影
- **禁用状态**: 50%透明度，禁用所有交互效果

## 🎨 颜色系统

### 按钮类型与颜色映射
| 类型 | 边框色 | 文字色 | 悬停背景色 | 用途 |
|------|--------|--------|------------|------|
| primary | #1976d2 | #1976d2 | #e3f2fd | 主要操作（必应奖励） |
| success | #388e3c | #388e3c | #e8f5e8 | 成功操作（读取记录） |
| warning | #f57c00 | #f57c00 | #fff3e0 | 警告操作（哔哩搜索） |
| info | #0097a7 | #0097a7 | #e0f2f1 | 信息操作（自动模式） |
| cache | #ff9800 | #ff9800 | #fff3e0 | 缓存操作（清除缓存） |
| error | #d32f2f | #d32f2f | #ffebee | 危险操作（关闭所有） |

## 📱 响应式断点

### 大屏幕 (>1200px)
- 标准布局，按钮高度38px
- 间距8px，字体13px

### 中等屏幕 (768px-1200px)
- 保持3列布局
- 按钮高度36px，间距6px
- 字体12px

### 小屏幕 (480px-768px)
- 继续保持3列布局
- 按钮高度34px，间距4px
- 字体11px

### 超小屏幕 (<480px)
- 最小化间距3px
- 按钮高度32px，字体10px
- 移除最小宽度限制

## 📐 布局结构

### 功能分组
```html
<!-- 第一行：核心功能按钮 -->
<div class="action-grid">
    <button class="action-btn primary">必应奖励</button>
    <button class="action-btn success">读取记录</button>
    <button class="action-btn warning">哔哩搜索</button>
</div>

<!-- 第二行：辅助功能按钮 -->
<div class="action-grid">
    <button class="action-btn info">自动模式</button>
    <button class="action-btn cache">清除缓存</button>
    <button class="action-btn error">关闭所有</button>
</div>
```

### 区域标题
```html
<div class="section-title">
    <span>批量操作</span>
    <span id="selectionCount">已选择: 0</span>
</div>
```

## 🔄 更新的文件

### 样式文件
- `assets/style.css` - 主要样式规范

### HTML文件
- `assets/index_fixed.html` - 主界面
- `assets/index_new.html` - 新版界面
- `assets/test_ui.html` - 测试界面

### 测试文件
- `assets/button_layout_test.html` - 布局测试页面

## ✅ 改进效果

1. **视觉一致性**: 所有按钮具有统一的外观和行为
2. **响应式优化**: 在各种屏幕尺寸下都能正常显示
3. **交互体验**: 流畅的动画效果和清晰的状态反馈
4. **代码维护**: 规范化的CSS结构，便于后续维护
5. **用户友好**: 符合现代UI设计标准，提升用户体验

## 🚀 使用建议

1. 在添加新按钮时，请遵循现有的颜色系统和样式规范
2. 保持2行3列的布局结构，避免破坏整体平衡
3. 新增按钮类型时，请在颜色系统中添加对应的样式定义
4. 测试时请使用 `button_layout_test.html` 验证布局效果

---

*本规范基于窗口化（非最大化）的默认大小进行设计，确保在实际使用场景中的最佳体验。*
