@echo off
chcp 437 >nul
echo CPL Layout Diagnosis Tool
echo ========================
echo.

echo 1. Checking file structure...
if exist "assets\style.css" (
    echo [OK] style.css exists
) else (
    echo [ERROR] style.css not found
)

if exist "assets\index_new.html" (
    echo [OK] index_new.html exists
) else (
    echo [ERROR] index_new.html not found
)

if exist "src\main.js" (
    echo [OK] main.js exists
) else (
    echo [ERROR] main.js not found
)

echo.
echo 2. Checking CSS file content...
findstr /C:"grid-template-columns: repeat(3, 1fr)" assets\style.css >nul
if %errorlevel%==0 (
    echo [OK] 3-column grid layout found in CSS
) else (
    echo [ERROR] 3-column grid layout not found in CSS
)

findstr /C:"height: 38px" assets\style.css >nul
if %errorlevel%==0 (
    echo [OK] 38px button height found in CSS
) else (
    echo [ERROR] 38px button height not found in CSS
)

findstr /C:"border-radius: 4px" assets\style.css >nul
if %errorlevel%==0 (
    echo [OK] 4px border radius found in CSS
) else (
    echo [ERROR] 4px border radius not found in CSS
)

echo.
echo 3. Checking HTML file content...
findstr /C:"style.css" assets\index_new.html >nul
if %errorlevel%==0 (
    echo [OK] CSS reference found in HTML
) else (
    echo [ERROR] CSS reference not found in HTML
)

findstr /C:"action-grid" assets\index_new.html >nul
if %errorlevel%==0 (
    echo [OK] action-grid class found in HTML
) else (
    echo [ERROR] action-grid class not found in HTML
)

echo.
echo 4. Checking main.js configuration...
findstr /C:"index_new.html" src\main.js >nul
if %errorlevel%==0 (
    echo [OK] index_new.html is loaded by main.js
) else (
    echo [ERROR] index_new.html not found in main.js
)

echo.
echo 5. File modification times:
for %%f in (assets\style.css assets\index_new.html src\main.js) do (
    echo %%f: 
    forfiles /m %%f /c "cmd /c echo @fdate @ftime" 2>nul
)

echo.
echo 6. Recommendations:
echo - Close all CPL instances completely
echo - Use restart_with_fresh_cache.bat to start with clean cache
echo - Check if antivirus is blocking file changes
echo - Verify you're running start.bat from the correct directory
echo.

echo 7. Quick test files:
echo - Open assets\button_layout_test.html to see expected layout
echo - Open assets\style_test.html to verify CSS loading
echo.

pause
