/* 桌面应用风格的CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    overflow: hidden;
    user-select: none;
    margin: 0;
    padding: 0;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #ffffff;
}

/* 拖拽区域 */
.drag-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #ffffff;
    border-bottom: 1px solid #d0d0d0; /* 细的浅灰色分隔线 */
    -webkit-app-region: drag; /* 允许拖拽 */
}

.app-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

/* 窗口控制按钮 */
.window-controls {
    display: flex;
    gap: 8px;
    -webkit-app-region: no-drag;
}

.window-btn {
    width: 46px;
    height: 32px;
    border: none;
    border-radius: 0;
    background: transparent;
    color: #000;
    font-size: 10px;
    font-weight: normal;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

}

.window-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.minimize-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.maximize-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
    background: #e81123;
    color: white;
}

/* 主内容区 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    gap: 1px;
    background: #f0f0f0; /* 浅灰色分隔线 */
}

/* 左侧面板 - 配置文件列表 */
.left-panel {
    width: 50%;
    background: #ffffff; /* 白色背景 */
    display: flex;
    flex-direction: column;
    border-right: 1px solid #f0f0f0; /* 浅灰色分隔线 */
}

/* 右侧面板 - 操作和日志区 */
.right-panel {
    flex: 1;
    background: #ffffff; /* 白色背景 */
    display: flex;
    flex-direction: column;
    height: 100vh; /* 确保占满整个视窗高度 */
}

/* 面板头部 */
.panel-header {
    height: 40px;
    background: #ffffff; /* 白色背景 */
    border-top: 1px solid #f0f0f0; /* 浅灰色分隔线 */
    border-bottom: 1px solid #f0f0f0; /* 浅灰色分隔线 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
}

.panel-title {
    font-size: 13px;
    font-weight: 600;
    color: #2d3748;
}

.panel-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    padding: 4px 8px;
    border: 1px solid #d0d0d0; /* 细的浅灰色边框 */
    background: #ffffff; /* 白色背景 */
    color: #333333;
    cursor: pointer;
    border-radius: 0; /* 移除圆角 */
    font-size: 11px;
}

.control-btn:hover {
    background: #f8f8f8;
    color: #000000;
    border-color: #999999;
}

/* 侧边栏 */
.sidebar {
    width: 320px;
    background: #ffffff;
    border-right: 1px solid #d0d0d0; /* 细的浅灰色分隔线 */
    padding: 24px;
    overflow-y: auto;
}

.sidebar-section h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.profile-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.profile-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.profile-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding: 8px;
    padding-left: 14px; /* 8px基础padding + 6px对齐边距 */
    padding-right: 14px; /* 保持对称 */
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    align-content: flex-start;
    width: 100%;
}

/* 每4个配置文件为一组的容器 */
.profile-group {
    display: flex;
    gap: 4px;
    width: calc((100% - 24px) / 3); /* 减去左右边距后的按钮宽度 */
    justify-content: space-between;
}

/* 配置文件项样式 */
.profile-item {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
}

.profile-item {
    width: 32px;
    height: 32px;
    border-radius: 0; /* 改为方形 */
    cursor: pointer;
    user-select: none;
    background: #ffffff; /* 白色背景 */
    border: 1px solid #f0c674; /* 浅黄色边框 */
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-item:hover {
    background: #ffd54f;
    border-color: #ffd54f;
}

.profile-item.selected {
    background: #ffd54f;
    border-color: #ffd54f;
}

/* 配置文件卡片内部样式 */
.profile-content {
    padding: 16px;
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.profile-number {
    font-size: 14px;
    font-weight: normal; /* 不加粗 */
    color: #f0c674; /* 黄色字体 */
    line-height: 1;
    text-align: center;
}

.profile-item:hover .profile-number,
.profile-item.selected .profile-number {
    color: #ffffff; /* 悬停和选中时使用白色字体 */
}

.profile-status {
    width: 8px;
    height: 8px;
    border-radius: 0; /* 改为方形 */
    background: #e0e0e0;
}

.profile-status.selected {
    background: #4caf50;
}

.profile-name {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.profile-info {
    font-size: 12px;
    color: #666;
}

.info-text {
    color: #666;
}

/* 工作区 */
.workspace {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 24px;
    gap: 24px;
    overflow: hidden;
    background: #ffffff; /* 白色背景 */
}

/* 操作区域 - 占据剩余的50%空间 */
.action-area {
    padding: 8px 12px;
    background: #ffffff; /* 白色背景 */
    flex: 1; /* 占据剩余空间 */
    overflow-y: auto; /* 允许滚动 */
    max-height: 50vh; /* 最大高度为视窗的50% */
}

.action-section {
    margin-bottom: 8px;
}

.section-title {
    font-size: 13px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* 选项区域 - 按照button_layout_test.html规范，使用统一的action-grid布局 */
.options-section {
    background: #ffffff; /* 白色背景 */
    padding: 0;
    padding-left: 6px; /* 与批量操作区对齐 */
    padding-right: 6px; /* 保持对称 */
    margin-top: 6px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
    align-items: start;
    width: 100%; /* 占满整个容器宽度，按网格线对齐 */
}

.option-item {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    user-select: none;
    padding: 0;
    margin-right: 8px;
    margin-bottom: 8px;
}

.option-checkbox {
    width: 100%;
    height: 28px;
    border: 1px solid #d0d0d0;
    border-radius: 0;
    background: #ffffff;
    color: #333333;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 500;
}

/* Headless按钮 - 蓝色，按照button_layout_test.html规范 */
#headlessBtn.primary,
#headlessCheckbox.checked {
    background: #2196f3;
    border-color: #2196f3;
    color: #ffffff;
}

#headlessBtn.primary:hover {
    background: #1976d2;
    border-color: #1976d2;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
}

/* Minimize按钮 - 绿色，按照button_layout_test.html规范 */
#minimizeBtn.success,
#minimizeCheckbox.checked {
    background: #4caf50;
    border-color: #4caf50;
    color: #ffffff;
}

#minimizeBtn.success:hover {
    background: #388e3c;
    border-color: #388e3c;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(56, 142, 60, 0.2);
}

.option-label {
    font-size: 13px;
    color: #2d3748;
    font-weight: 500;
    flex: 1;
}

/* 按钮网格布局 - 规范化2行3列布局 */
.action-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 8px; /* 统一间距为8px */
    margin-bottom: 8px; /* 行间距 */
    padding: 0 8px; /* 左右内边距，保持对称 */
}

/* 按钮基础样式 - 统一规范 */
.action-btn {
    height: 38px; /* 稍微增加高度，提升点击体验 */
    border: 1px solid #d0d0d0;
    background: #ffffff;
    color: #333333;
    border-radius: 4px; /* 添加轻微圆角，更现代化 */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    font-weight: 500;
    box-shadow: none;
    transition: all 0.2s ease; /* 添加过渡动画 */
    min-width: 0; /* 防止文字溢出 */
    white-space: nowrap; /* 防止文字换行 */
}

/* 按钮悬停效果 - 统一规范 */
.action-btn:hover {
    background: #f5f5f5;
    border-color: #999999;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 添加轻微阴影 */
    transform: translateY(-1px); /* 轻微上移效果 */
}

/* 按钮主题色彩 - 规范化颜色系统 */
.action-btn.primary {
    background: #ffffff;
    border-color: #1976d2;
    color: #1976d2;
}

.action-btn.primary:hover {
    background: #e3f2fd;
    border-color: #1565c0;
    color: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
}

.action-btn.success {
    background: #ffffff;
    border-color: #388e3c;
    color: #388e3c;
}

.action-btn.success:hover {
    background: #e8f5e8;
    border-color: #2e7d32;
    color: #2e7d32;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(56, 142, 60, 0.2);
}

.action-btn.warning {
    background: #ffffff;
    border-color: #f57c00;
    color: #f57c00;
}

.action-btn.warning:hover {
    background: #fff3e0;
    border-color: #ef6c00;
    color: #ef6c00;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(245, 124, 0, 0.2);
}

.action-btn.error {
    background: #ffffff;
    border-color: #d32f2f;
    color: #d32f2f;
}

.action-btn.error:hover {
    background: #ffebee;
    border-color: #c62828;
    color: #c62828;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.2);
}

.action-btn.info {
    background: #ffffff;
    border-color: #0097a7;
    color: #0097a7;
}

.action-btn.info:hover {
    background: #e0f2f1;
    border-color: #00838f;
    color: #00838f;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 151, 167, 0.2);
}

.action-btn.cache {
    background: #ffffff;
    border-color: #ff9800;
    color: #ff9800;
}

.action-btn.cache:hover {
    background: #fff3e0;
    border-color: #f57c00;
    color: #f57c00;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.2);
}

/* 按钮区域容器样式 */
.action-section {
    margin-bottom: 16px;
}

.action-section .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 8px;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
}

/* 按钮激活状态 */
.action-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 按钮禁用状态 */
.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.action-btn:disabled:hover {
    background: #ffffff;
    border-color: #d0d0d0;
    transform: none;
    box-shadow: none;
}

/* 日志区域 - 占据整个页面的50%空间 */
.log-panel {
    height: 50vh; /* 占据视窗高度的50% */
    background: #ffffff; /* 白色背景 */
    border-top: 1px solid #f0f0f0; /* 浅灰色分隔线 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex-shrink: 0; /* 防止被压缩 */
}

.log-header {
    height: 32px;
    background: #ffffff; /* 白色背景 */
    border-bottom: 1px solid #f0f0f0; /* 浅灰色分隔线 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    flex-shrink: 0;
}

.log-title {
    font-size: 12px;
    font-weight: 600;
    color: #2d3748;
}

.log-controls {
    display: flex;
    gap: 6px;
}

.log-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    background: #ffffff;
}

.log-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    padding: 3px 0;
    gap: 4px;
}

.log-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    /* 确保图标渲染清晰 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.log-icon svg {
    width: 14px;
    height: 14px;
    stroke-width: 2;
    /* 确保SVG渲染清晰 */
    shape-rendering: geometricPrecision;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

.log-message {
    color: #2c3e50;
    flex: 1;
    line-height: 1.5;
    display: flex;
    align-items: center;
}

.log-item.info .log-icon svg {
    stroke: #2196f3;
}

.log-item.info .log-message {
    color: #2196f3;
}

.log-item.success .log-icon svg {
    stroke: #4caf50;
}

.log-item.success .log-message {
    color: #4caf50;
}

.log-item.warning .log-icon svg {
    stroke: #ff9800;
}

.log-item.warning .log-message {
    color: #ff9800;
}

.log-item.error .log-icon svg {
    stroke: #f44336;
}

.log-item.error .log-message {
    color: #f44336;
}

/* 结果面板 */
.results-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 400px;
    max-height: 500px;
    background: white;
    border-radius: 0; /* 移除圆角 */
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f5f7ff;
    border-bottom: 1px solid #d0d0d0; /* 细的浅灰色分隔线 */
    border-radius: 0; /* 移除圆角 */
}

.results-content {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

/* 移除状态栏样式 */

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: 1px solid #d0d0d0; /* 细的浅灰色边框 */
    border-radius: 0; /* 移除圆角 */
    background: #ffffff; /* 白色背景 */
    color: #333333;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
}

.btn:hover {
    background: #f8f8f8;
    border-color: #999999;
    color: #000000;
}

.btn.btn-sm {
    padding: 4px 8px;
    font-size: 11px;
}

/* 加载遮罩 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 0; /* 移除圆角 */
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}



.loading-text {
    color: #374151;
    font-weight: 500;
}

/* 表格样式 */
.results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.results-table th,
.results-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0; /* 浅灰色分隔线 */
    font-size: 12px;
}

.results-table th {
    background: #ffffff; /* 白色背景 */
    font-weight: 600;
    color: #333333;
    border-bottom: 1px solid #d0d0d0; /* 细的浅灰色分隔线 */
}

.status-badge {
    padding: 3px 8px;
    border-radius: 0; /* 移除圆角 */
    font-size: 10px;
    font-weight: 500;
}

.status-success { background: #dcfce7; color: #166534; }
.status-failed { background: #fef2f2; color: #991b1b; }
.status-new { background: #dbeafe; color: #1e40af; }
.status-removed { background: #fef3c7; color: #92400e; }

/* 图标 */
.icon {
    display: inline-block;
    font-style: normal;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0; /* 移除圆角 */
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 0; /* 移除圆角 */
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 响应式设计 - 规范化不同屏幕尺寸下的布局 */
@media (max-width: 1200px) {
    .sidebar {
        width: 240px;
    }

    .left-panel {
        width: 45%;
    }

    /* 保持3列布局，调整间距 */
    .action-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 6px;
        padding: 0 6px;
    }

    .action-btn {
        height: 36px;
        font-size: 12px;
    }

    .results-panel {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: 200px;
    }

    .left-panel {
        width: 100%;
        height: 250px;
    }

    /* 小屏幕保持3列布局，优化间距和尺寸 */
    .action-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 4px;
        padding: 0 4px;
        margin-bottom: 6px;
    }

    .action-btn {
        height: 34px;
        font-size: 11px;
        border-radius: 3px;
    }

    .results-panel {
        position: relative;
        width: 100%;
        margin-top: 20px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .action-grid {
        gap: 3px;
        padding: 0 3px;
    }

    .action-btn {
        height: 32px;
        font-size: 10px;
        min-width: 0;
    }
}
