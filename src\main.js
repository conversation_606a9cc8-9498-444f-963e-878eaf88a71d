const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// 恢复所有依赖项
const Config = require('./config');
const ChromeLauncher = require('./chrome-launcher');

// 保持对窗口对象的全局引用
let mainWindow;
let pythonProcess;
let chromeLauncher;

function createWindow() {
    // 检查命令行参数
    const useMinWidth = process.argv.includes('--min-width');
    const isWindowed = process.argv.includes('--windowed');

    // 根据参数设置窗口大小
    const windowWidth = useMinWidth ? 550 : 650;
    const windowHeight = 750;

    // 创建浏览器窗口
    mainWindow = new BrowserWindow({
        width: windowWidth,
        height: windowHeight,
        minWidth: 550,
        minHeight: 650,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets/icon.png'),
        show: false, // 先不显示，等加载完成后再显示
        backgroundColor: '#ffffff', // 设置背景色为白色
        autoHideMenuBar: true // 自动隐藏菜单栏
    });

    // 强制禁用缓存
    mainWindow.webContents.session.clearCache();
    mainWindow.webContents.session.clearStorageData();

    // 设置缓存禁用
    mainWindow.webContents.session.setUserAgent(mainWindow.webContents.session.getUserAgent() + ' NoCache');

    // 加载应用的 index.html
    mainWindow.loadFile('assets/index_new.html');

    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // 初始化Chrome启动器
    chromeLauncher = new ChromeLauncher();
    chromeLauncher.setLogCallback((message, type) => {
        // 发送日志消息到渲染进程
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('log-message', { message, type });
        }
    });

    // 当窗口被关闭时发出事件
    mainWindow.on('closed', () => {
        mainWindow = null;
        // 关闭Python进程
        if (pythonProcess) {
            pythonProcess.kill();
        }
    });

    // 阻止新窗口打开，在默认浏览器中打开链接
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

// Electron 初始化完成并准备创建浏览器窗口时调用
app.whenReady().then(createWindow);

// 当所有窗口都关闭时退出应用
app.on('window-all-closed', () => {
    // 在 macOS 上，应用和菜单栏通常会保持活动状态，直到用户明确退出
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，通常会重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC 通信处理












ipcMain.handle('get-chrome-profiles', async () => {
    try {
        const { spawn } = require('child_process');
        const pythonProcess = spawn('python', ['src/get_profiles.py'], {
            cwd: path.join(__dirname, '..')
        });

        let output = '';
        let error = '';

        return new Promise((resolve) => {
            pythonProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            pythonProcess.stderr.on('data', (data) => {
                error += data.toString();
            });

            pythonProcess.on('close', (code) => {
                if (code === 0 && output.trim()) {
                    try {
                        const profiles = JSON.parse(output.trim());
                        resolve({ success: true, profiles: profiles });
                    } catch (parseError) {
                        resolve({ success: false, error: 'JSON解析失败' });
                    }
                } else {
                    resolve({ success: false, error: error || '脚本执行失败' });
                }
            });

            // 超时处理
            setTimeout(() => {
                pythonProcess.kill();
                resolve({ success: false, error: '检测超时' });
            }, 10000);
        });
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('read-records', async () => {
    try {
        const { spawn } = require('child_process');
        const pythonProcess = spawn('python', ['src/read_records.py'], {
            cwd: path.join(__dirname, '..')
        });

        let output = '';
        let error = '';

        return new Promise((resolve) => {
            pythonProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            pythonProcess.stderr.on('data', (data) => {
                error += data.toString();
            });

            pythonProcess.on('close', (code) => {
                if (code === 0 && output.trim()) {
                    try {
                        const result = JSON.parse(output.trim());
                        resolve(result);
                    } catch (parseError) {
                        resolve({
                            success: false,
                            message: 'JSON解析失败',
                            data: null
                        });
                    }
                } else {
                    resolve({
                        success: false,
                        message: error || '脚本执行失败',
                        data: null
                    });
                }
            });

            // 超时处理
            setTimeout(() => {
                pythonProcess.kill();
                resolve({
                    success: false,
                    message: '读取超时',
                    data: null
                });
            }, 15000);
        });
    } catch (error) {
        return {
            success: false,
            message: error.message,
            data: null
        };
    }
});

// 设置无头模式
ipcMain.handle('set-headless-mode', async (event, enabled) => {
    try {
        if (chromeLauncher) {
            chromeLauncher.setHeadlessMode(enabled);
            return { success: true, message: `无头模式已${enabled ? '启用' : '禁用'}` };
        }
        return { success: false, message: 'Chrome启动器未初始化' };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 设置最小化模式
ipcMain.handle('set-minimize-mode', async (event, enabled) => {
    try {
        if (chromeLauncher) {
            chromeLauncher.setMinimizeMode(enabled);
            return { success: true, message: `最小化模式已${enabled ? '启用' : '禁用'}` };
        }
        return { success: false, message: 'Chrome启动器未初始化' };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 启动必应奖励 - 完全对应Python版本逻辑
ipcMain.handle('launch-bing-rewards', async (event, profiles) => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        // 创建今日文件夹
        await chromeLauncher.createTodayFolder();

        // 设置并发数
        chromeLauncher.setMaxWorkers(Config.MAX_BING_WORKERS);

        // 定义单个配置文件的任务
        const launchTask = async (profileDisplay) => {
            const profileReal = Config.getProfileRealName(profileDisplay);
            try {
                await chromeLauncher.launchProfileWithWebsite(profileReal, Config.BING_REWARDS_URL);
                return { profile: profileDisplay, success: true };
            } catch (error) {
                return { profile: profileDisplay, success: false, error: error.message };
            }
        };

        // 使用并发执行
        const results = await chromeLauncher.executeWithConcurrency(profiles, launchTask, Config.MAX_BING_WORKERS);

        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        return {
            success: true,
            message: `必应奖励启动完成 (成功: ${successCount}, 失败: ${failCount})`,
            results,
            successCount,
            failCount
        };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 启动哔哩搜索 - 完全对应Python版本逻辑
ipcMain.handle('launch-bilibili-search', async (event, profiles) => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        // 创建今日文件夹
        await chromeLauncher.createTodayFolder();

        // 设置并发数
        chromeLauncher.setMaxWorkers(Config.MAX_BILIBILI_WORKERS);

        // 哔哩搜索的URL序列
        const urls = [Config.BILIBILI_SEARCH_URL];

        // 定义单个配置文件的任务
        const searchTask = async (profileDisplay) => {
            const profileReal = Config.getProfileRealName(profileDisplay);
            try {
                await chromeLauncher.visitUrlsSequence(profileReal, urls, true);
                // 添加配置文件间延迟
                await chromeLauncher.delay(Config.BILIBILI_PROFILE_DELAY);
                return { profile: profileDisplay, success: true };
            } catch (error) {
                return { profile: profileDisplay, success: false, error: error.message };
            }
        };

        // 使用并发执行
        const results = await chromeLauncher.executeWithConcurrency(profiles, searchTask, Config.MAX_BILIBILI_WORKERS);

        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        return {
            success: true,
            message: `哔哩搜索完成 (成功: ${successCount}, 失败: ${failCount})`,
            results,
            successCount,
            failCount
        };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 自动模式 - 完全对应Python版本逻辑
ipcMain.handle('auto-mode', async (event, profiles) => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        // 创建今日文件夹
        await chromeLauncher.createTodayFolder();

        // 设置并发数
        chromeLauncher.setMaxWorkers(Config.MAX_AUTO_WORKERS);

        // 自动模式的完整URL流程
        const autoModeUrls = [
            Config.BILIBILI_SEARCH_URL,  // 第一步：哔哩搜索
            Config.AUTO_SEARCH_URL,      // 第二步：自动搜索
            Config.BING_REWARDS_URL      // 第三步：必应奖励
        ];

        // 定义单个配置文件的任务
        const autoTask = async (profileDisplay) => {
            const profileReal = Config.getProfileRealName(profileDisplay);
            try {
                // 执行完整的自动化流程
                await chromeLauncher.visitUrlsSequence(profileReal, autoModeUrls, true);

                // 添加配置文件间延迟
                await chromeLauncher.delay(Config.AUTO_MODE_PROFILE_DELAY);

                return { profile: profileDisplay, success: true };
            } catch (error) {
                return { profile: profileDisplay, success: false, error: error.message };
            }
        };

        // 使用并发执行
        const results = await chromeLauncher.executeWithConcurrency(profiles, autoTask, Config.MAX_AUTO_WORKERS);

        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        return {
            success: true,
            message: `自动模式完成 (成功: ${successCount}, 失败: ${failCount})`,
            results,
            successCount,
            failCount
        };
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 清除今日缓存
ipcMain.handle('clear-today-cache', async () => {
    try {
        if (!chromeLauncher) {
            return { success: false, message: 'Chrome启动器未初始化' };
        }

        const result = await chromeLauncher.clearTodayCache();
        return result;
    } catch (error) {
        return { success: false, message: error.message };
    }
});

// 关闭所有Chrome
ipcMain.handle('close-all-chrome', async () => {
    try {
        if (chromeLauncher) {
            await chromeLauncher.closeAllChrome();
            await chromeLauncher.closeHeadlessProcesses();
            return { success: true, message: '所有Chrome进程已关闭' };
        }
        return { success: false, message: 'Chrome启动器未初始化' };
    } catch (error) {
        return { success: false, message: error.message };
    }
});



// 保存文件对话框
ipcMain.handle('show-save-dialog', async () => {
    const result = await dialog.showSaveDialog(mainWindow, {
        title: '保存日志',
        defaultPath: `ProfileLauncher_Log_${new Date().toISOString().slice(0, 10)}.txt`,
        filters: [
            { name: '文本文件', extensions: ['txt'] },
            { name: '所有文件', extensions: ['*'] }
        ]
    });
    return result;
});

// 保存文件
ipcMain.handle('save-file', async (event, filePath, content) => {
    try {
        const fs = require('fs').promises;
        await fs.writeFile(filePath, content, 'utf8');
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

// 防止应用被多次启动
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        // 当运行第二个实例时，将焦点放在主窗口上
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}
