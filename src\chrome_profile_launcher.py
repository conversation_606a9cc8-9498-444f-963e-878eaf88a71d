#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CPL - Chrome配置文件批量管理工具
一键启动多个Chrome配置文件并打开Bing奖励和哔哩搜索
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import subprocess
from datetime import datetime, timedelta
import time
import re
import threading
import tempfile
import shutil


class Config:
    """配置常量"""
    # 路径配置
    DEFAULT_CHROME_PATH = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    STALK_DIR = r"C:\Users\<USER>\Downloads\RTBS"
    
    # URL配置
    BILIBILI_SEARCH_URL = "https://www.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW"
    BING_REWARDS_URL = "https://rewards.bing.com"
    AUTO_SEARCH_URL = "https://cn.bing.com/search?q=%E4%BD%A0%E5%B9%B2%E5%98%9B%E5%93%8E%E5%93%9F&qs=n&form=QBRE&sp=-1&lq=0&pq=%E4%BD%A0%E5%B9%B2%E5%98%9B%E5%93%8E%E5%93%9F&sc=12-5&sk=&cvid=0AC6297DC24E403BAD29E6EADD6B1153"
    ACCOUNT_SIGNUP_URL = "https://signup.live.com/signup?cobrandid=03c8bbb5-2dff-4721-8261-a4ccff24c81a&contextid=24CAE7F75BFB5E1B&opid=1DDAB8E653226068&bk=**********&sru=https://login.live.com/oauth20_authorize.srf%3fclient_id%3d9c941f7c-a811-4e9c-8e66-29fdec50490f%26cobrandid%3d03c8bbb5-2dff-4721-8261-a4ccff24c81a%26client_id%3d9c941f7c-a811-4e9c-8e66-29fdec50490f%26cobrandid%3d03c8bbb5-2dff-4721-8261-a4ccff24c81a%26contextid%3d24CAE7F75BFB5E1B%26opid%3d1DDAB8E653226068%26mkt%3dZH-CN%26lc%3d2052%26bk%3d**********%26uaid%3d04a7c5ab27b34de9b18ad72294d6ac06&lw=easi2&fl=1&uiflavor=web&fluent=2&client_id=00000000407BC851&lic=1&mkt=ZH-CN&lc=2052&uaid=04a7c5ab27b34de9b18ad72294d6ac06"

    # 时间配置
    BROWSER_START_DELAY = 3
    PAGE_LOAD_DELAY = 3
    PROCESS_END_DELAY = 5
    AUTO_READ_DELAY = 10
    HEADLESS_CLEANUP_DELAY = 60

    # UI配置
    WINDOW_SIZE = "800x700"
    MAIN_PADDING = "8"
    LOG_HEIGHT = 18
    LOG_WIDTH = 70
    BUTTON_WIDTH = 80
    BUTTON_HEIGHT = 32
    
    # 左右分栏配置
    LEFT_PANEL_WIDTH = 200
    RIGHT_PANEL_WIDTH = 320


class ChromeProfileLauncher:
    """Chrome配置文件启动器主类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("CPL")
        self.root.geometry(Config.WINDOW_SIZE)

        # 变量
        self.chrome_path = tk.StringVar(value=Config.DEFAULT_CHROME_PATH)
        self.headless_mode = tk.BooleanVar(value=False)
        self.profiles = self.detect_chrome_profiles()

        # 如果没有检测到配置文件，使用默认列表
        if not self.profiles:
            self.profiles = ["Default"]

        self.create_widgets()

        # 启动后显示检测结果
        self.root.after(100, self.show_detection_result)

        # 初始化无头模式进程列表
        self.headless_processes = []

    def detect_chrome_profiles(self):
        """检测Chrome用户数据目录中的所有配置文件"""
        try:
            # Chrome用户数据目录路径
            user_data_paths = [
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data"),
                os.path.expanduser(r"~\AppData\Local\Google\Chrome Beta\User Data"),
                os.path.expanduser(r"~\AppData\Local\Google\Chrome Dev\User Data"),
                os.path.expanduser(r"~\AppData\Local\Google\Chrome Canary\User Data")
            ]

            profiles = []

            for user_data_path in user_data_paths:
                if os.path.exists(user_data_path):
                    # 检查Default配置文件
                    default_path = os.path.join(user_data_path, "Default")
                    if os.path.exists(default_path):
                        profiles.append("Default")

                    # 检查Profile X配置文件
                    for item in os.listdir(user_data_path):
                        item_path = os.path.join(user_data_path, item)
                        if os.path.isdir(item_path) and item.startswith("Profile "):
                            # 验证是否是有效的配置文件目录
                            preferences_file = os.path.join(item_path, "Preferences")
                            if os.path.exists(preferences_file):
                                profiles.append(item)

                    # 如果找到了配置文件，就使用第一个找到的Chrome版本
                    if profiles:
                        break

            # 去重并自然排序
            profiles = list(set(profiles))

            def natural_sort_key(profile):
                """自然排序键函数，确保1-10.11.12的正确排序"""
                if profile == "Default":
                    return (0, 0)  # Default排在最前面
                elif profile.startswith("Profile "):
                    try:
                        num = int(profile.split(" ")[1])
                        return (1, num)  # 按数字排序
                    except (IndexError, ValueError):
                        return (2, profile)  # 其他格式排在最后
                else:
                    return (2, profile)  # 其他格式排在最后

            profiles.sort(key=natural_sort_key)
            return profiles

        except Exception as e:
            return ["Default"]  # 出错时返回默认配置文件

    def _get_chrome_headless_args(self, temp_dir, download_dir):
        """获取Chrome无头模式的启动参数"""
        return [
            f"--user-data-dir={temp_dir}",
            "--headless=new",
            "--disable-gpu",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-extensions",
            "--disable-plugins",
            "--no-first-run",
            "--disable-default-apps",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            f"--download-directory={download_dir}",
            "--enable-automation",
            "--disable-blink-features=AutomationControlled",
            "--remote-debugging-port=0",
            "--disable-background-networking",
            "--disable-sync",
            "--metrics-recording-only",
            "--no-report-upload",
            "--safebrowsing-disable-auto-update"
        ]

    def _launch_chrome_instance(self, profile_name, url=None):
        """启动Chrome实例的通用方法"""
        try:
            chrome_path = self.chrome_path.get()
            if not os.path.exists(chrome_path):
                raise FileNotFoundError(f"Chrome路径不存在: {chrome_path}")

            if self.headless_mode.get():
                # 无头模式
                temp_dir = tempfile.mkdtemp(prefix="chrome_headless_")
                download_dir = Config.STALK_DIR
                
                if not os.path.exists(download_dir):
                    os.makedirs(download_dir)

                cmd = [chrome_path] + self._get_chrome_headless_args(temp_dir, download_dir)
                if url:
                    cmd.append(url)

                process = subprocess.Popen(cmd, shell=False)
                self.headless_processes.append({'process': process, 'temp_dir': temp_dir})
                return process
            else:
                # 普通模式
                cmd = [chrome_path, f"--profile-directory={profile_name}"]
                if url:
                    cmd.append(url)
                return subprocess.Popen(cmd, shell=False)

        except Exception as e:
            self._handle_error("启动Chrome", e)
            raise

    def _get_url_display_name(self, url):
        """获取URL的简化显示名称"""
        if "bing.com/search" in url and "%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9" in url:
            return "哔哩搜索"
        elif "cn.bing.com/search" in url and "%E4%BD%A0%E5%B9%B2%E5%98%9B%E5%93%8E%E5%93%9F" in url:
            return "你干嘛哎哟搜索"
        elif "rewards.bing.com" in url:
            return "必应奖励"
        elif "signup.live.com" in url:
            return "账号注册"
        else:
            return "网页"

    def _visit_urls_sequence(self, profile_name, urls, close_after=True):
        """按顺序访问URL列表的通用方法"""
        try:
            if not self.headless_mode.get():
                # 普通模式：先启动浏览器
                self._launch_chrome_instance(profile_name)
                time.sleep(Config.BROWSER_START_DELAY)

            # 访问每个URL
            for i, url in enumerate(urls):
                display_name = self._get_url_display_name(url)
                self.log_message(f"  访问 {display_name}")
                
                if self.headless_mode.get():
                    # 无头模式：为每个URL启动独立实例
                    self._launch_chrome_instance(profile_name, url)
                else:
                    # 普通模式：在现有浏览器中打开新标签
                    subprocess.Popen([self.chrome_path.get(), f"--profile-directory={profile_name}", url], shell=False)
                
                time.sleep(Config.PAGE_LOAD_DELAY)

            # 等待处理完成
            if close_after:
                time.sleep(Config.PROCESS_END_DELAY)
                display_profile = self._get_profile_display_name(profile_name)
                self.log_message(f"成功: 配置文件 {display_profile} 的流程已完成")

        except Exception as e:
            display_profile = self._get_profile_display_name(profile_name)
            self.log_message(f"失败: 配置文件 {display_profile} 的URL访问流程失败: {str(e)}")
            raise

    def _handle_error(self, operation, error, show_dialog=False):
        """统一的错误处理方法"""
        error_msg = f"失败: {operation}失败: {str(error)}"
        self.log_message(error_msg)

        if show_dialog:
            messagebox.showerror("错误", error_msg)

        return False

    def _get_profile_display_name(self, profile_name):
        """将配置文件名转换为显示名称"""
        if profile_name == "Default":
            return "1"
        elif profile_name.startswith("Profile "):
            try:
                # 提取Profile后的数字，然后加1
                profile_num = int(profile_name.split(" ")[1])
                return str(profile_num + 1)
            except (IndexError, ValueError):
                return profile_name
        else:
            return profile_name

    def _get_profile_real_name(self, display_name):
        """将显示名称转换回真实配置文件名"""
        try:
            num = int(display_name)
            if num == 1:
                return "Default"
            else:
                return f"Profile {num - 1}"
        except ValueError:
            return display_name

    def create_widgets(self):
        """创建UI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding=Config.MAIN_PADDING)
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建左右分栏布局
        # 左侧面板 - 配置文件选择
        left_frame = ttk.LabelFrame(main_frame, text="配置文件选择", padding="8")
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 4))

        # 右侧面板 - 操作区域
        right_frame = ttk.LabelFrame(main_frame, text="批量操作", padding="8")
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(4, 0))

        # === 左侧配置文件选择区域 ===
        self.profile_vars = {}

        # 全选/取消全选按钮
        select_control_frame = ttk.Frame(left_frame)
        select_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(select_control_frame, text="全选", command=self.select_all, width=8).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(select_control_frame, text="清空", command=self.deselect_all, width=8).grid(row=0, column=1)

        # 配置文件列表 - 垂直排列，每个配置文件只显示数字
        profiles_frame = ttk.Frame(left_frame)
        profiles_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        for i, profile in enumerate(self.profiles):
            var = tk.BooleanVar(value=False)
            self.profile_vars[profile] = var
            display_name = self._get_profile_display_name(profile)
            ttk.Checkbutton(profiles_frame, text=f"配置文件 {display_name}", variable=var).grid(
                row=i, column=0, sticky=tk.W, pady=2
            )

        # === 右侧操作区域 ===
        # 操作按钮 - 2x4布局，使用标准按钮样式
        buttons_frame = ttk.Frame(right_frame)
        buttons_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 8))

        # 创建所有按钮
        buttons = [
            ("必应奖励", self.launch_selected_with_bing_rewards),
            ("读取记录", self.read_records),
            ("哔哩搜索", self.launch_selected_with_bilibili_search),
            ("自动模式", self.auto_mode),
            ("账号注册", self.launch_selected_with_account_signup),
            ("清除缓存", self.clear_today_cache),
            ("关闭所有", self.close_all_chrome),
            ("", None)  # 占位按钮，保持2x4布局
        ]

        # 2x4布局：2行4列
        for i, (text, command) in enumerate(buttons):
            row = i // 4
            col = i % 4
            if text and command:  # 只创建有效按钮
                btn = ttk.Button(buttons_frame, text=text, command=command, width=11)
                btn.grid(row=row, column=col, padx=3, pady=3, sticky=(tk.W, tk.E))

        # 配置按钮框架的列权重，确保按钮均匀分布
        for col in range(4):
            buttons_frame.columnconfigure(col, weight=1)

        # 无头模式选项
        headless_frame = ttk.Frame(right_frame)
        headless_frame.grid(row=1, column=0, pady=(5, 0))
        ttk.Checkbutton(headless_frame, text="Chrome无头模式（后台运行）", variable=self.headless_mode).pack()

        # 日志区域 - 放在右侧批量操作区域下方
        log_frame = ttk.LabelFrame(right_frame, text="操作日志", padding="8")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(8, 0))

        # 创建日志文本框和滚动条
        log_container = ttk.Frame(log_frame)
        log_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.log_text = tk.Text(log_container, wrap=tk.WORD,
                               font=("Consolas", 9), bg="white", fg="black")
        log_scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 日志控制按钮
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.grid(row=1, column=0, pady=(5, 0))

        ttk.Button(log_btn_frame, text="清空", command=self.clear_log).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(log_btn_frame, text="保存", command=self.save_log).grid(row=0, column=1)

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)  # 左侧面板
        main_frame.columnconfigure(1, weight=1)  # 右侧面板
        main_frame.rowconfigure(0, weight=1)     # 主要内容区域可扩展

        # 左侧面板权重
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(1, weight=1)  # 配置文件列表可扩展

        # 右侧面板权重
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(0, weight=0)  # 按钮区域固定高度
        right_frame.rowconfigure(1, weight=0)  # 无头模式选项固定高度
        right_frame.rowconfigure(2, weight=1)  # 日志区域可扩展

        # 日志区域权重
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        log_container.columnconfigure(0, weight=1)
        log_container.rowconfigure(0, weight=1)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        # 检查消息中是否包含"哔哩搜索"，如果有则使用彩色显示
        if "哔哩搜索" in message:
            parts = formatted_message.split("哔哩搜索")
            self.log_text.insert(tk.END, parts[0])

            start_pos = self.log_text.index(tk.END + "-1c")
            self.log_text.insert(tk.END, "哔哩搜索")
            end_pos = self.log_text.index(tk.END + "-1c")
            self.log_text.tag_add("bilibili_search", start_pos, end_pos)
            self.log_text.tag_config("bilibili_search", foreground="#FF69B4")

            if len(parts) > 1:
                self.log_text.insert(tk.END, "哔哩搜索".join(parts[1:]))
        else:
            self.log_text.insert(tk.END, formatted_message)

        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def log_separator(self, title=""):
        """添加分割线"""
        if title:
            separator = f"{'='*20} {title} {'='*20}\n"
        else:
            separator = f"{'='*50}\n"

        self.log_text.insert(tk.END, separator)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def select_all(self):
        """全选配置文件"""
        for var in self.profile_vars.values():
            var.set(True)
        self.log_message("已全选所有配置文件")

    def deselect_all(self):
        """取消全选配置文件"""
        for var in self.profile_vars.values():
            var.set(False)
        self.log_message("已取消选择所有配置文件")

    def get_selected_profiles(self):
        """获取选中的配置文件"""
        try:
            selected = []
            for profile, var in self.profile_vars.items():
                if var.get():
                    selected.append(profile)
            return selected
        except Exception as e:
            self._handle_error("获取选中的配置文件", e)
            return []

    def show_detection_result(self):
        """显示配置文件检测结果"""
        # 转换配置文件名称为显示名称
        display_names = [self._get_profile_display_name(profile) for profile in self.profiles]
        if len(self.profiles) > 1:
            self.log_message(f"检测到 {len(self.profiles)} 个Chrome配置文件: {', '.join(display_names)}")
        else:
            self.log_message(f"检测到 {len(self.profiles)} 个Chrome配置文件: {', '.join(display_names)}")

        # 读取并对比日期文件
        self.compare_daily_files()
        self.log_message("程序已就绪，可以开始批量操作")

    def clear_log(self):
        """清空日志"""
        try:
            self.log_text.delete(1.0, tk.END)
            self.log_message("日志已清空")
        except Exception as e:
            messagebox.showerror("错误", f"清空日志失败: {str(e)}")

    def save_log(self):
        """保存日志到文件"""
        try:
            log_content = self.log_text.get(1.0, tk.END)
            if not log_content.strip():
                messagebox.showwarning("警告", "日志为空，无需保存")
                return

            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            if not os.path.exists(desktop_path):
                desktop_path = os.getcwd()

            filename = f"chrome_launcher_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            file_path = os.path.join(desktop_path, filename)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"Chrome配置文件启动器 - 操作日志\n")
                f.write(f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")
                f.write(log_content)

            self.log_message(f"成功: 日志已保存到: {filename}")
            messagebox.showinfo("保存成功", f"日志已保存到桌面:\n{filename}")

        except Exception as e:
            self.log_message(f"失败: 保存日志失败: {str(e)}")
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")

    def read_records(self):
        """读取当天文件夹内所有txt文件的记录，使用表格格式并与昨日数据对比"""
        try:
            self.log_separator("读取记录")
            # 直接调用表格格式的对比方法
            self.compare_daily_files_table_format()
            self.log_separator()
        except Exception as e:
            self.log_message(f"失败: 读取记录失败: {str(e)}")
            self.log_separator()

    def launch_selected_with_bing_rewards(self):
        """启动选中的配置文件并打开Bing奖励"""
        try:
            selected_profiles = self.get_selected_profiles()
            if not selected_profiles:
                messagebox.showwarning("警告", "请至少选择一个配置文件")
                return

            # 执行前准备：创建文件夹
            self.log_message("准备: 执行必应奖励前的准备工作...")
            self.create_today_folder()

            self.log_message(f"启动: 批量启动 {len(selected_profiles)} 个配置文件并打开Bing奖励...")

            # 使用简单的多线程启动，避免GUI阻塞
            threads = []

            for i, profile in enumerate(selected_profiles):
                display_profile = self._get_profile_display_name(profile)
                self.log_message(f"启动: 启动配置文件: {display_profile}")

                def launch_with_delay(p=profile, d=i*2):
                    if d > 0:
                        time.sleep(d)
                    self.launch_profile_with_website(p, Config.BING_REWARDS_URL)

                thread = threading.Thread(target=launch_with_delay, daemon=True)
                thread.start()
                threads.append(thread)

            self.log_message(f"完成: 所有配置文件已开始启动，共 {len(selected_profiles)} 个")

            # 在无头模式下，等待一段时间后自动关闭浏览器
            if self.headless_mode.get():
                self.log_message(f"无头模式: 将在{Config.HEADLESS_CLEANUP_DELAY}秒后自动关闭必应奖励浏览器进程...")
                self.root.after(Config.HEADLESS_CLEANUP_DELAY * 1000, self.close_headless_bing_rewards)

            # 自动执行读取记录（使用表格格式）
            self.log_message(f"将在{Config.AUTO_READ_DELAY}秒后自动读取记录...")
            self.root.after(Config.AUTO_READ_DELAY * 1000, self.auto_read_records_table_format)

        except Exception as e:
            self.log_message(f"失败: 批量启动并打开Bing奖励失败: {str(e)}")
            messagebox.showerror("错误", f"批量启动并打开Bing奖励失败: {str(e)}")

    def launch_selected_with_bilibili_search(self):
        """启动选中的配置文件并执行哔哩搜索流程"""
        try:
            selected_profiles = self.get_selected_profiles()
            if not selected_profiles:
                messagebox.showwarning("警告", "请至少选择一个配置文件")
                return

            self.log_message(f"开始: 开始执行哔哩搜索流程，共 {len(selected_profiles)} 个配置文件...")

            # 使用简单的多线程启动，避免GUI阻塞
            threads = []

            for i, profile in enumerate(selected_profiles):
                display_profile = self._get_profile_display_name(profile)
                self.log_message(f"启动: 启动配置文件: {display_profile}")

                def execute_with_delay(p=profile, d=i*3):
                    if d > 0:
                        time.sleep(d)
                    self.execute_bilibili_search_flow(p)

                thread = threading.Thread(target=execute_with_delay, daemon=True)
                thread.start()
                threads.append(thread)

            self.log_message(f"完成: 所有配置文件已开始哔哩搜索，共 {len(selected_profiles)} 个")

        except Exception as e:
            self.log_message(f"失败: 哔哩搜索流程启动失败: {str(e)}")
            messagebox.showerror("错误", f"哔哩搜索流程启动失败: {str(e)}")

    def execute_bilibili_search_flow(self, profile_name):
        """执行单个配置文件的哔哩搜索流程"""
        try:
            display_profile = self._get_profile_display_name(profile_name)

            # 第一次访问哔哩搜索
            self.log_message(f"  第一次访问哔哩搜索")
            self._launch_chrome_instance(profile_name, Config.BILIBILI_SEARCH_URL)

            # 等待2秒后进行第二次访问
            time.sleep(2)

            # 第二次访问哔哩搜索（启动新的Chrome实例）
            self.log_message(f"  第二次访问哔哩搜索")
            self._launch_chrome_instance(profile_name, Config.BILIBILI_SEARCH_URL)

            self.log_message(f"成功: 配置文件 {display_profile} 的哔哩搜索流程已完成")

        except Exception as e:
            display_profile = self._get_profile_display_name(profile_name)
            self.log_message(f"失败: 配置文件 {display_profile} 的哔哩搜索流程失败: {str(e)}")
            raise

    def launch_selected_with_account_signup(self):
        """启动选中的配置文件并打开账号注册页面"""
        try:
            selected_profiles = self.get_selected_profiles()
            if not selected_profiles:
                messagebox.showwarning("警告", "请至少选择一个配置文件")
                return

            self.log_message(f"注册: 开始启动账号注册页面，共 {len(selected_profiles)} 个配置文件...")

            # 使用简单的多线程启动，避免GUI阻塞
            threads = []

            for i, profile in enumerate(selected_profiles):
                display_profile = self._get_profile_display_name(profile)
                self.log_message(f"注册: 启动配置文件: {display_profile}")

                def launch_signup_with_delay(p=profile, d=i*2):
                    if d > 0:
                        time.sleep(d)
                    self.launch_profile_with_website(p, Config.ACCOUNT_SIGNUP_URL)

                thread = threading.Thread(target=launch_signup_with_delay, daemon=True)
                thread.start()
                threads.append(thread)

            self.log_message(f"完成: 所有配置文件已开始启动账号注册页面，共 {len(selected_profiles)} 个")

        except Exception as e:
            self.log_message(f"失败: 账号注册页面启动失败: {str(e)}")
            messagebox.showerror("错误", f"账号注册页面启动失败: {str(e)}")

    def auto_mode(self):
        """自动模式：按顺序访问URL，然后读取记录，最后关闭浏览器"""
        try:
            selected_profiles = self.get_selected_profiles()
            if not selected_profiles:
                messagebox.showwarning("警告", "请至少选择一个配置文件")
                return

            self.log_separator("自动模式")
            self.log_message(f"开始: 开始自动模式，共 {len(selected_profiles)} 个配置文件...")

            # 执行前准备：创建文件夹
            self.log_message(f"准备: 执行自动模式前的准备工作...")
            self.create_today_folder()

            # 使用简单的多线程启动，避免GUI阻塞
            threads = []

            for i, profile in enumerate(selected_profiles):
                display_profile = self._get_profile_display_name(profile)
                self.log_message(f"启动: 启动配置文件: {display_profile}")

                def execute_auto_with_delay(p=profile, d=i*4):
                    if d > 0:
                        time.sleep(d)
                    self.execute_auto_mode_flow(p)

                thread = threading.Thread(target=execute_auto_with_delay, daemon=True)
                thread.start()
                threads.append(thread)

            self.log_message(f"完成: 所有配置文件已开始自动模式，共 {len(selected_profiles)} 个")

            # 无头模式清理（如果需要）
            if self.headless_mode.get():
                self.log_message(f"无头模式: 5秒后清理浏览器实例")
                self.root.after(5000, self.close_headless_bing_rewards)

        except Exception as e:
            self.log_message(f"失败: 自动模式启动失败: {str(e)}")
            self.log_separator()

    def execute_auto_mode_flow(self, profile_name):
        """执行单个配置文件的完整自动模式流程"""
        try:
            # 定义要访问的URL列表（按顺序）
            urls = [Config.BILIBILI_SEARCH_URL, Config.AUTO_SEARCH_URL, Config.BING_REWARDS_URL]

            display_profile = self._get_profile_display_name(profile_name)
            self.log_message(f"开始: 配置文件 {display_profile} 开始自动模式流程...")

            # 使用通用方法访问URL序列
            self._visit_urls_sequence(profile_name, urls, close_after=False)

            # 等待处理完成后执行读取记录
            time.sleep(Config.PROCESS_END_DELAY)
            self.log_message(f"等待: 配置文件 {display_profile} 等待{Config.PROCESS_END_DELAY}秒后执行读取记录...")

            # 执行读取记录（只在第一个配置文件完成时执行一次）
            if profile_name == self.get_selected_profiles()[0]:
                self.log_message(f"开始: 开始读取记录...")
                self.auto_read_records_with_comparison()

            self.log_message(f"完成: 配置文件 {display_profile} 的自动模式流程已完成")

        except Exception as e:
            display_profile = self._get_profile_display_name(profile_name)
            self.log_message(f"失败: 配置文件 {display_profile} 的自动模式流程失败: {str(e)}")
            raise

    def launch_profile_with_website(self, profile_name, url):
        """启动指定配置文件并打开指定网站"""
        try:
            # 简化URL显示
            display_url = self._get_url_display_name(url)
            display_profile = self._get_profile_display_name(profile_name)
            self.log_message(f"启动配置文件 {display_profile} 并打开 {display_url}")

            # 使用通用Chrome启动方法
            self._launch_chrome_instance(profile_name, url)
            self.log_message(f"成功: 配置文件 {display_profile} 启动成功并打开网站")

        except Exception as e:
            display_profile = self._get_profile_display_name(profile_name)
            self.log_message(f"失败: 启动配置文件 {display_profile} 并打开网站失败: {str(e)}")
            raise

    def close_all_chrome(self):
        """关闭所有Chrome进程"""
        try:
            self.log_message("尝试关闭所有Chrome进程...")

            # 使用taskkill命令关闭Chrome进程
            result = subprocess.run(["taskkill", "/f", "/im", "chrome.exe"],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                self.log_message("成功: 所有Chrome进程已关闭")
            else:
                self.log_message("警告: 没有找到Chrome进程或关闭失败")

        except Exception as e:
            self.log_message(f"失败: 关闭Chrome进程失败: {str(e)}")
            messagebox.showerror("错误", f"关闭Chrome进程失败: {str(e)}")

    def create_today_folder(self):
        """创建今日文件夹"""
        try:
            rtbs_dir = Config.STALK_DIR
            today = datetime.now()
            today_str = f"{today.month}月{today.day}日"
            today_folder = os.path.join(rtbs_dir, today_str)

            # 确保RTBS目录存在
            if not os.path.exists(rtbs_dir):
                os.makedirs(rtbs_dir)
                self.log_message(f"成功: 创建RTBS目录: {rtbs_dir}")

            # 创建今日文件夹（如果不存在）
            if not os.path.exists(today_folder):
                os.makedirs(today_folder)
                self.log_message(f"成功: 创建今日文件夹: {today_str}")
            else:
                self.log_message(f"信息: 今日文件夹已存在: {today_str}")

        except Exception as e:
            self.log_message(f"警告: 创建文件夹时出错: {str(e)}")

    def clear_today_cache(self):
        """清除当日文件夹内的所有txt文件"""
        try:
            rtbs_dir = Config.STALK_DIR
            today = datetime.now()
            today_str = f"{today.month}月{today.day}日"
            today_folder = os.path.join(rtbs_dir, today_str)

            # 检查今日文件夹是否存在
            if not os.path.exists(today_folder):
                self.log_message(f"信息: 今日文件夹 {today_str} 不存在，无需清除缓存")
                return

            # 查找所有txt文件
            txt_files = []
            for file_name in os.listdir(today_folder):
                if file_name.lower().endswith('.txt'):
                    txt_files.append(os.path.join(today_folder, file_name))

            if not txt_files:
                self.log_message(f"信息: 今日文件夹 {today_str} 中没有txt文件，无需清除")
                return

            self.log_message(f"删除: 开始清除今日文件夹 {today_str} 中的 {len(txt_files)} 个txt文件...")

            # 删除所有txt文件
            deleted_count = 0
            failed_files = []

            for txt_file in txt_files:
                try:
                    os.remove(txt_file)
                    deleted_count += 1
                    file_name = os.path.basename(txt_file)
                    self.log_message(f"删除: 已删除: {file_name}")
                except Exception as e:
                    failed_files.append(os.path.basename(txt_file))
                    self.log_message(f"错误: 删除失败: {os.path.basename(txt_file)} - {str(e)}")

            # 显示结果
            if failed_files:
                self.log_message(f"警告: 清除缓存完成：成功删除 {deleted_count} 个文件，{len(failed_files)} 个文件删除失败")
            else:
                self.log_message(f"完成: 清除缓存完成：成功删除 {deleted_count} 个txt文件")

        except Exception as e:
            self.log_message(f"错误: 清除缓存失败: {str(e)}")
            messagebox.showerror("错误", f"清除缓存失败: {str(e)}")

    def close_headless_bing_rewards(self):
        """关闭无头模式下的浏览器进程并清理临时目录"""
        try:
            if self.headless_mode.get() and hasattr(self, 'headless_processes'):
                self.log_message("无头模式: 正在清理浏览器进程和临时文件...")

                cleaned_count = 0
                for process_info in self.headless_processes:
                    try:
                        process = process_info['process']
                        temp_dir = process_info['temp_dir']

                        # 终止进程
                        if process.poll() is None:  # 进程仍在运行
                            process.terminate()
                            try:
                                process.wait(timeout=5)  # 等待5秒
                            except subprocess.TimeoutExpired:
                                process.kill()  # 强制杀死

                        # 清理临时目录
                        if temp_dir and os.path.exists(temp_dir):
                            shutil.rmtree(temp_dir, ignore_errors=True)

                        cleaned_count += 1
                    except Exception as e:
                        self.log_message(f"警告: 清理单个进程时出错: {str(e)}")

                # 清空进程列表
                self.headless_processes = []

                self.log_message(f"成功: 已清理 {cleaned_count} 个无头模式浏览器实例")
                self.log_message("完成: 无头模式清理完毕，用户Chrome不受影响")
            else:
                self.log_message("信息: 非无头模式或无进程需要清理")
        except Exception as e:
            self.log_message(f"警告: 清理无头模式浏览器时出现问题: {str(e)}")

    def auto_read_records_table_format(self):
        """自动读取记录并使用表格格式显示对比数据"""
        try:
            self.log_separator("读取记录")
            self.log_message(f"开始: 开始读取记录并对比数据...")
            # 直接调用表格格式的对比方法
            self.compare_daily_files_table_format()
            self.log_separator()
        except Exception as e:
            self.log_message(f"失败: 自动读取记录失败: {str(e)}")
            self.log_separator()

    def auto_read_records_with_comparison(self):
        """自动读取记录并与昨日数据对比"""
        self.auto_read_records_table_format()

    def compare_daily_files(self):
        """读取并对比今日和昨日的日期文件夹中的txt文件"""
        self.compare_daily_files_table_format()

    def compare_daily_files_table_format(self):
        """读取并对比今日和昨日的日期文件夹中的txt文件，使用表格格式显示"""
        try:
            rtbs_dir = Config.STALK_DIR
            if not os.path.exists(rtbs_dir):
                return

            # 获取今日和昨日的日期字符串
            today = datetime.now()
            yesterday = today - timedelta(days=1)

            today_str = f"{today.month}月{today.day}日"
            yesterday_str = f"{yesterday.month}月{yesterday.day}日"

            today_folder = os.path.join(rtbs_dir, today_str)
            yesterday_folder = os.path.join(rtbs_dir, yesterday_str)

            # 读取今日文件夹中的所有txt文件
            today_data = self.read_folder_account_data(today_folder)
            if today_data is None:
                self.log_message(f"信息: 今日文件夹 {today_str} 不存在或为空")
                return

            # 读取昨日文件夹中的所有txt文件
            yesterday_data = self.read_folder_account_data(yesterday_folder)
            if yesterday_data is None:
                self.log_message(f"信息: 昨日文件夹 {yesterday_str} 不存在或为空")
                self.log_message(f"今日账号数据:")
                # 按天数排序显示
                sorted_accounts = sorted(today_data.items(), key=lambda x: x[1], reverse=True)
                for i, (account, days) in enumerate(sorted_accounts, 1):
                    self.log_message(f"  {i:2d}. {account}: {days}天")
                return

            # 对比数据并按天数排序
            self.log_message(f"账号数据对比 ({yesterday_str} vs {today_str}):")
            # 表格标题
            self.log_message("=" * 70)
            self.log_message(f"{'序号':<4} {'账号':<30} {'天数':<8} {'状态':<10}")
            self.log_message("=" * 70)

            # 获取所有账号（今日和昨日的并集），按今日天数排序
            if yesterday_data is None:
                all_accounts = set(today_data.keys())
            else:
                all_accounts = set(today_data.keys()) | set(yesterday_data.keys())

            # 创建账号列表，按天数排序（天数最大的排在上面）
            account_list = []
            for account in all_accounts:
                today_days = today_data.get(account, 0)
                yesterday_days = yesterday_data.get(account, 0) if yesterday_data else 0
                account_list.append((account, today_days, yesterday_days))

            # 按今日天数降序排序
            account_list.sort(key=lambda x: x[1], reverse=True)

            for i, (account, today_days, yesterday_days) in enumerate(account_list, 1):
                # 确定状态和颜色
                if yesterday_data is None or account not in yesterday_data:
                    # 新账号
                    status = "New"
                    color = "blue"
                elif account not in today_data:
                    # 账号消失
                    status = "已移除"
                    color = "red"
                    today_days = 0
                elif today_days == yesterday_days + 1:
                    # 正常增加（签到成功）
                    status = "签到成功"
                    color = "green"
                elif today_days == yesterday_days:
                    # 天数相同，检查是否是7#7这种情况（通过检查文件名）
                    is_max_days = self._check_if_max_days_reached(account, today_folder)
                    if is_max_days:
                        status = "签到结束"
                        color = "blue"
                    else:
                        status = "签到失败"
                        color = "red"
                else:
                    # 其他情况
                    change = today_days - yesterday_days
                    if change > 1:
                        status = f"增加{change}天"
                        color = "green"
                    else:
                        status = f"减少{abs(change)}天"
                        color = "red"

                # 格式化显示
                line = f"{i:<2d}. {account:<30} {today_days:<8} {status:<10}"
                self.log_message_with_color(line, color)

            self.log_message("=" * 70)
            self.log_message(f"读取记录完成，共检查 {len(account_list)} 个账号")

        except Exception as e:
            self.log_message(f"警告: 对比日期文件时出错: {str(e)}")

    def _check_if_max_days_reached(self, account, folder_path):
        """检查账号是否达到最大天数（如7#7这种情况）"""
        try:
            if not os.path.exists(folder_path):
                return False

            for filename in os.listdir(folder_path):
                if not filename.endswith('.txt'):
                    continue

                # 移除重复下载标识
                base_name = filename[:-4]
                base_name = re.sub(r'[（(]\d+[）)]$', '', base_name)

                if '---' not in base_name:
                    continue

                try:
                    account_part, days_part = base_name.split('---', 1)
                    if account_part.strip() == account and '#' in days_part:
                        # 检查是否是 x#x 格式（当前天数等于总天数）
                        parts = days_part.split('#')
                        if len(parts) == 2:
                            current_days = int(parts[0])
                            total_days = int(parts[1])
                            if current_days == total_days:
                                return True
                except (ValueError, IndexError):
                    continue

            return False
        except Exception:
            return False

    def read_folder_account_data(self, folder_path):
        """读取文件夹中所有txt文件的账号数据，返回字典格式 {账号: 天数}
        处理重复下载文件（忽略(1)(2)等后缀），对同一账号选择最大天数"""
        try:
            if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                return None

            account_data = {}

            # 遍历文件夹中的所有txt文件
            for filename in os.listdir(folder_path):
                if not filename.endswith('.txt'):
                    continue

                # 解析文件名格式: "账号---天数#总天数"
                # 例如: "<EMAIL>---3#7.txt" 或 "<EMAIL>---4#7（6）.txt"
                base_name = filename[:-4]  # 去掉.txt后缀

                # 移除重复下载标识，如 (1), (2), （1）, （2） 等
                # 匹配各种括号格式的重复下载标识
                base_name = re.sub(r'[（(]\d+[）)]$', '', base_name)

                if '---' not in base_name:
                    continue

                try:
                    account_part, days_part = base_name.split('---', 1)
                    account_name = account_part.strip()

                    # 解析天数部分，可能是 "2#7" 或 "2" 格式
                    if '#' in days_part:
                        current_days = int(days_part.split('#')[0])
                    else:
                        current_days = int(days_part)

                    # 对同一账号，保留最大天数
                    if account_name in account_data:
                        if current_days > account_data[account_name]:
                            account_data[account_name] = current_days
                    else:
                        account_data[account_name] = current_days

                except (ValueError, IndexError):
                    # 如果解析失败，跳过这个文件
                    continue

            return account_data if account_data else None

        except Exception as e:
            return None

    def log_message_with_color(self, message, color):
        """添加带颜色的日志消息"""
        # 插入彩色消息
        start_pos = self.log_text.index(tk.END + "-1c")
        self.log_text.insert(tk.END, message)
        end_pos = self.log_text.index(tk.END + "-1c")

        # 设置颜色标签
        tag_name = f"color_{color}"
        self.log_text.tag_add(tag_name, start_pos, end_pos)

        if color == "red":
            self.log_text.tag_config(tag_name, foreground="#FF0000")  # 红色
        elif color == "green":
            self.log_text.tag_config(tag_name, foreground="#008000")  # 绿色
        elif color == "blue":
            self.log_text.tag_config(tag_name, foreground="#0000FF")  # 蓝色

        self.log_text.insert(tk.END, "\n")
        # 自动滚动到底部
        self.log_text.see(tk.END)


def main():
    """主函数"""
    root = tk.Tk()
    app = ChromeProfileLauncher(root)
    root.mainloop()


if __name__ == "__main__":
    main()
