# Chrome Profile Launcher

🚀 **专业的Chrome配置文件批量管理工具**

一个为Bing奖励和多账号管理而设计的高效工具，支持智能检测、批量启动、自动化流程和数据记录管理。提供桌面版（Electron）和命令行版（Python）两种使用方式。

---

## ✨ 核心特性

### 🎯 **智能配置文件管理**
- **自动检测** Chrome用户数据目录中的所有配置文件
- **智能排序** 按自然数字顺序排列（1→10→11，而非1→10→2）
- **简化显示** Default显示为1，Profile 1显示为2，依此类推
- **批量操作** 支持同时管理多达数十个配置文件

### 🌐 **专业网站访问**
- **必应奖励** 一键启动所有配置文件并打开Bing奖励页面
- **哔哩搜索** 批量执行哔哩哔哩相关搜索任务
- **自动模式** 按序执行搜索→奖励→数据读取的完整流程
- **无头模式** 后台运行，不干扰用户正常使用Chrome

### 📊 **数据记录与分析**
- **自动读取** 每日签到记录文件
- **智能对比** 与昨日数据对比，显示账号状态变化
- **重复处理** 自动处理重复下载文件，选择最大天数
- **状态标识** 清晰显示新增、增加、未变化、减少等状态

### 📝 **优雅的日志系统**
- **简洁显示** 无时间戳干扰，专注操作内容
- **统一图标** 使用📝图标，视觉一致美观
- **彩色标识** "哔哩搜索"等关键词彩色显示
- **完整记录** 支持保存到文件，便于追踪历史

---

## 🖥️ 界面预览

### 配置文件选择区域
```
批量操作
☑ 1    ☑ 2    ☑ 3    ☑ 4
☑ 5    ☑ 6    ☑ 7    ☑ 8
☑ 9    ☑ 10   ☑ 11   ☑ 12

[一键全选] [取消全选] [读取记录] [必应奖励]
[哔哩搜索] [自动模式] [关闭所有]

☑ Chrome无头模式（后台运行）
```

### 操作日志区域
```
操作日志
📝 检测到 12 个Chrome配置文件: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12
📝 账号数据对比 (7月25日 vs 7月26日):
📝    <EMAIL>: 3天 → 4天 📈增加
📝    <EMAIL>: 5天 ➖未增加
📝 启动配置文件: 1
📝 访问 哔哩搜索
📝 配置文件 1 的流程已完成

[清空日志] [保存日志]
```

---

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/11
- **Node.js**: 16.0 或更高版本（桌面版）
- **Python**: 3.7 或更高版本（命令行版）
- **浏览器**: Google Chrome

### 桌面版（推荐）
```bash
# 1. 安装依赖
npm install

# 2. 启动应用
npm start

# 或者直接运行
start.bat
```

### 命令行版
```bash
# 直接运行Python脚本
python src/chrome_profile_launcher.py
```

### 首次使用
1. **自动检测** 程序启动后自动扫描Chrome配置文件
2. **选择配置** 勾选需要操作的配置文件
3. **选择功能** 点击对应按钮执行批量操作
4. **查看日志** 在日志区域查看操作结果和数据对比

---

## 🎮 功能详解

### 📦 批量操作按钮

| 按钮 | 功能 | 说明 |
|------|------|------|
| **一键全选** | 选择所有配置文件 | 快速选中所有检测到的配置文件 |
| **取消全选** | 取消所有选择 | 清除所有配置文件的选中状态 |
| **读取记录** | 读取今日数据 | 分析今日文件夹中的签到记录 |
| **必应奖励** | 启动Bing奖励 | 批量打开Bing奖励页面并自动读取记录 |
| **哔哩搜索** | 执行搜索流程 | 批量执行哔哩哔哩相关搜索任务 |
| **自动模式** | 完整自动流程 | 按序执行搜索→奖励→读取的完整流程 |
| **关闭所有** | 强制关闭Chrome | 终止所有Chrome进程 |

### 🔧 高级功能

#### **无头模式**
- ✅ 后台运行Chrome，不影响用户正常使用
- ✅ 自动管理临时文件和进程清理
- ✅ 独立的用户数据目录，避免冲突

#### **数据分析**
- ✅ 自动解析文件名格式：`账号---天数#总天数.txt`
- ✅ 处理重复下载文件（忽略(1)(2)等后缀）
- ✅ 智能对比昨日数据，显示变化趋势
- ✅ 支持多种状态：新增、增加、未变化、减少、移除

#### **错误处理**
- ✅ 统一的异常处理机制
- ✅ 详细的错误信息显示
- ✅ 防御性编程，确保程序稳定性

---

## 📁 项目结构

```
Chrome Profile Launcher/
├── src/                          # 源代码目录
│   ├── chrome_profile_launcher.py   # Python主程序
│   ├── main.js                      # Electron主进程
│   ├── config.js                    # 配置文件
│   ├── chrome-launcher.js           # Chrome启动器
│   ├── renderer.js                  # 渲染进程
│   ├── get_profiles.py              # 配置文件检测
│   └── read_records.py              # 记录读取
├── assets/                       # 静态资源
│   ├── index_fixed.html             # 主界面
│   └── style.css                    # 样式文件
├── scripts/                      # 启动脚本
│   └── start_electron.bat           # Electron启动脚本
├── start.bat                     # 主启动脚本
├── package.json                  # Node.js配置
├── requirements.txt              # Python依赖
└── README.md                     # 项目文档
```

### 核心模块

| 模块 | 功能 |
|------|------|
| `Config` | 配置常量管理（路径、URL、时间等） |
| `ChromeProfileLauncher` | Python主程序类，包含所有核心功能 |
| `ChromeLauncher` | JavaScript Chrome启动器类 |

---

## 🛠️ 技术特性

### 🔍 **智能检测算法**
```python
# 自动扫描Chrome用户数据目录
user_data_paths = [
    "~\AppData\Local\Google\Chrome\User Data",
    "~\AppData\Local\Google\Chrome Beta\User Data",
    "~\AppData\Local\Google\Chrome Dev\User Data",
    "~\AppData\Local\Google\Chrome Canary\User Data"
]
```

### 📊 **自然排序算法**
```python
# 确保1-10.11.12的正确排序
def natural_sort_key(profile):
    if profile == "Default": return (0, 0)
    elif profile.startswith("Profile "):
        num = int(profile.split(" ")[1])
        return (1, num)
    else: return (2, profile)
```

### 🎯 **配置文件名转换**
```python
# 简化显示名称
Default → 1
Profile 1 → 2
Profile N → N+1
```

---

## 📈 使用场景

### 🏆 **Bing奖励管理**
- **多账号签到** 一键完成所有账号的每日签到
- **奖励查看** 批量查看各账号的奖励状态
- **数据追踪** 自动记录和对比每日签到数据

### 🔍 **搜索任务执行**
- **批量搜索** 在多个账号下执行相同搜索任务
- **自动化流程** 按预设顺序自动完成搜索和奖励流程
- **效率提升** 并行处理，大幅提升操作效率

### 📊 **数据分析监控**
- **状态监控** 实时监控各账号的签到状态
- **趋势分析** 对比历史数据，发现异常情况
- **报告生成** 自动生成日志报告，便于追踪

---

## ⚙️ 配置说明

### 📂 **路径配置**
- **Chrome路径**: `C:\Program Files\Google\Chrome\Application\chrome.exe`
- **数据目录**: `C:\Users\<USER>\Downloads\RTBS`
- **日志保存**: 桌面 `chrome_launcher_log_YYYYMMDD_HHMMSS.txt`

### ⏱️ **时间配置**
- **浏览器启动延迟**: 3秒
- **页面加载延迟**: 3秒
- **自动读取延迟**: 10秒
- **无头模式清理**: 60秒

### 🌐 **URL配置**
- **哔哩搜索**: `https://www.bing.com/search?q=哔哩哔哩`
- **必应奖励**: `https://rewards.bing.com`
- **其他搜索**: 可在Config类中自定义

---

## 🔒 安全与隐私

- ✅ **本地运行** 所有操作在本地执行，无数据上传
- ✅ **独立进程** 无头模式使用独立临时目录
- ✅ **自动清理** 程序结束后自动清理临时文件
- ✅ **用户控制** 所有操作需用户主动触发

---

## 📋 版本信息

| 项目 | 信息 |
|------|------|
| **版本** | Chrome Profile Launcher v2.0 |
| **开发语言** | Python 3.7+ / JavaScript (Node.js) |
| **支持平台** | Windows 10/11 |
| **界面框架** | Electron / Tkinter |
| **代码行数** | 1500+ 行（优化后） |
| **更新日期** | 2025年7月 |

---

## ⚠️ 免责声明

本程序仅用于方便管理Chrome配置文件，提高多账号操作效率。使用时请：

- 遵守相关网站的服务条款和使用协议
- 合理使用自动化功能，避免过度频繁操作
- 定期备份重要数据，程序不对数据丢失负责
- 在使用前确保了解程序功能和操作后果

---

## 🎉 特别感谢

感谢所有使用和反馈的用户，您的建议让这个工具变得更加完善！

**Chrome Profile Launcher** - 让Chrome多配置文件管理变得简单高效！ 🚀
