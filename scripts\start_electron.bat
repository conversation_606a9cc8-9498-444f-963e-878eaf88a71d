@echo off
chcp 65001 >nul
echo Starting CPL...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Starting application...
node_modules\.bin\electron.cmd . --min-width
if errorlevel 1 (
    echo Application failed to start
    pause
    exit /b 1
)

echo Application has exited.
pause
