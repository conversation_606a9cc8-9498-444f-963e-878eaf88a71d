<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPL</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            background: #ffffff;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            padding: 20px;
            border: 1px solid #f0f0f0;
        }
        .debug-info {
            background: #f8f8f8;
            padding: 10px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="debug-info">
            测试：一行三个按钮，总计两行的布局<br>
            当前窗口宽度：<span id="windowWidth"></span>px<br>
            CSS Grid设置：grid-template-columns: repeat(3, 1fr)
        </div>
        
        <div class="action-section">
            <div class="section-title">
                <span>批量操作</span>
                <span>已选择: 0</span>
            </div>
            
            <!-- 第一行：3个按钮 -->
            <div class="action-grid">
                <button class="action-btn primary">必应奖励</button>
                <button class="action-btn success">读取记录</button>
                <button class="action-btn warning">哔哩搜索</button>
            </div>
            
            <!-- 第二行：3个按钮 -->
            <div class="action-grid">
                <button class="action-btn info">自动模式</button>
                <button class="action-btn">清除缓存</button>
                <button class="action-btn error">关闭所有</button>
            </div>
        </div>
        
        <div class="debug-info" style="margin-top: 20px;">
            如果看到的是2列布局，说明媒体查询仍在生效。<br>
            如果看到的是3列布局，说明修改成功。
        </div>
    </div>

    <script>
        // 显示当前窗口宽度
        function updateWidth() {
            document.getElementById('windowWidth').textContent = window.innerWidth;
        }
        updateWidth();
        window.addEventListener('resize', updateWidth);
    </script>
</body>
</html>
