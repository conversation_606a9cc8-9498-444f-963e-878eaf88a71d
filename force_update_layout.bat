@echo off
chcp 437 >nul
echo Force Update CPL Layout
echo ======================
echo.

echo Step 1: Killing all existing processes...
taskkill /f /im electron.exe >nul 2>&1
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 3 /nobreak >nul

echo Step 2: Clearing all caches...
if exist "%APPDATA%\CPL" (
    echo Clearing app data cache...
    rmdir /s /q "%APPDATA%\CPL" >nul 2>&1
)

if exist "node_modules\.cache" (
    echo Clearing node cache...
    rmdir /s /q "node_modules\.cache" >nul 2>&1
)

echo Step 3: Updating CSS version timestamp...
powershell -Command "(Get-Content 'assets\index_new.html') -replace 'style\.css\?v=\d+', 'style.css?v=%date:~0,4%%date:~5,2%%date:~8,2%%time:~0,2%%time:~3,2%' | Set-Content 'assets\index_new.html'"

echo Step 4: Verifying file updates...
findstr /C:"grid-template-columns: repeat(3, 1fr)" assets\style.css >nul
if %errorlevel%==0 (
    echo [OK] Updated CSS layout found
) else (
    echo [ERROR] CSS layout not updated - please check assets\style.css
    pause
    exit /b 1
)

echo Step 5: Starting CPL with fresh cache...
echo.
echo Expected layout: 2 rows x 3 columns button grid
echo Button height: 38px with 4px border radius
echo.

node_modules\.bin\electron.cmd . --min-width --disable-http-cache --disable-gpu-sandbox --no-sandbox
if errorlevel 1 (
    echo.
    echo Application failed to start. Trying alternative method...
    npm start
)

echo.
echo If you still see the old layout:
echo 1. Try opening assets\button_layout_test.html in browser
echo 2. Check if your antivirus is blocking file changes
echo 3. Restart your computer and try again
echo.
pause
