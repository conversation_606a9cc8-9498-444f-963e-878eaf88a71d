@echo off
chcp 437 >nul
echo Starting CPL...
echo Layout Version: 2024072701 (Normalized Button Layout)
echo.

REM Kill any existing instances
taskkill /f /im electron.exe >nul 2>&1

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Starting Electron application with fresh cache...
node_modules\.bin\electron.cmd . --min-width --disable-http-cache
if errorlevel 1 (
    echo Application failed to start
    pause
    exit /b 1
)

echo Application has exited.
pause