# CPL 按钮布局更新指南

## 🎯 问题描述
您的程序显示的是旧的按钮布局，而不是新的规范化2行3列布局。

## 🔍 问题原因
1. **缓存问题**: Electron应用可能缓存了旧的CSS样式
2. **进程残留**: 旧的Electron进程可能仍在运行
3. **文件加载**: 程序可能没有正确加载更新后的样式文件

## ✅ 解决方案

### 方案1: 使用强制更新脚本（推荐）
```bash
# 运行强制更新脚本
force_update_layout.bat
```

### 方案2: 使用重启脚本
```bash
# 运行带缓存清除的重启脚本
restart_with_fresh_cache.bat
```

### 方案3: 手动步骤
1. **完全关闭程序**
   - 关闭所有CPL窗口
   - 打开任务管理器，结束所有 `electron.exe` 进程

2. **清除缓存**
   - 删除 `%APPDATA%\CPL` 文件夹（如果存在）
   - 删除 `node_modules\.cache` 文件夹（如果存在）

3. **重新启动**
   - 使用更新后的 `start.bat` 启动程序

## 📋 验证步骤

### 1. 检查文件更新
运行诊断脚本：
```bash
diagnose_layout.bat
```

### 2. 测试布局效果
在浏览器中打开测试文件：
- `assets/button_layout_test.html` - 查看预期布局
- `assets/style_test.html` - 验证CSS加载状态

### 3. 确认布局特征
新布局应该具有以下特征：
- ✅ 2行3列的按钮网格
- ✅ 按钮高度38px
- ✅ 按钮间距8px
- ✅ 4px圆角
- ✅ 悬停时上移效果和阴影
- ✅ 规范化的颜色系统

## 🔧 已更新的文件

### 核心文件
- `assets/style.css` - 主样式文件（规范化按钮布局）
- `assets/index_new.html` - 主界面HTML（添加缓存控制）
- `src/main.js` - 主进程（增强缓存清除）
- `start.bat` - 启动脚本（添加缓存禁用）

### 辅助文件
- `restart_with_fresh_cache.bat` - 强制重启脚本
- `force_update_layout.bat` - 强制更新脚本
- `diagnose_layout.bat` - 诊断脚本
- `assets/button_layout_test.html` - 布局测试页面
- `assets/style_test.html` - 样式测试页面

## 🎨 新布局规范

### 按钮分组
**第一行（核心功能）:**
- 必应奖励 (primary - 蓝色)
- 读取记录 (success - 绿色)  
- 哔哩搜索 (warning - 橙色)

**第二行（辅助功能）:**
- 自动模式 (info - 青色)
- 清除缓存 (cache - 橙色)
- 关闭所有 (error - 红色)

### 技术规格
```css
.action-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 0 8px;
}

.action-btn {
    height: 38px;
    border-radius: 4px;
    transition: all 0.2s ease;
}
```

## 🚨 故障排除

### 如果布局仍然是旧的
1. **检查进程**: 确保没有残留的electron.exe进程
2. **检查文件**: 确认 `assets/style.css` 包含新的样式
3. **检查引用**: 确认 `assets/index_new.html` 正确引用CSS
4. **重启系统**: 如果以上都无效，尝试重启计算机

### 如果程序无法启动
1. **检查依赖**: 运行 `npm install` 重新安装依赖
2. **检查路径**: 确保在正确的目录下运行脚本
3. **检查权限**: 确保有足够的文件读写权限

## 📞 技术支持

如果问题仍然存在，请提供以下信息：
1. 运行 `diagnose_layout.bat` 的输出结果
2. 打开 `assets/style_test.html` 的截图
3. 程序实际显示的布局截图

---

**版本信息**: 2024072701 - 规范化按钮布局版本  
**更新时间**: 2024年7月27日
