<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮布局测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .layout-demo {
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .demo-label {
            font-size: 14px;
            color: #4a5568;
            margin-bottom: 12px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">按钮布局规范测试</h1>
        
        <div class="layout-demo">
            <div class="demo-label">标准窗口布局 (2行3列)</div>
            <div class="action-section">
                <div class="section-title">
                    <span>批量操作</span>
                    <span>已选择: 3</span>
                </div>
                <!-- 第一行：核心功能按钮 -->
                <div class="action-grid">
                    <button class="action-btn primary">必应奖励</button>
                    <button class="action-btn success">读取记录</button>
                    <button class="action-btn warning">哔哩搜索</button>
                </div>
                <!-- 第二行：辅助功能按钮 -->
                <div class="action-grid">
                    <button class="action-btn info">自动模式</button>
                    <button class="action-btn cache">清除缓存</button>
                    <button class="action-btn error">关闭所有</button>
                </div>
            </div>
        </div>
        
        <div class="layout-demo">
            <div class="demo-label">选项区域</div>
            <div class="action-section">
                <div class="section-title">
                    <span>选项</span>
                </div>
                <div class="action-grid">
                    <button class="action-btn" id="headlessBtn">Headless</button>
                    <button class="action-btn" id="minimizeBtn">Minimize</button>
                    <button class="action-btn" style="opacity: 0.3; cursor: default;"></button>
                </div>
            </div>
        </div>
        
        <div class="layout-demo">
            <div class="demo-label">按钮状态演示</div>
            <div class="action-section">
                <div class="action-grid">
                    <button class="action-btn primary">正常状态</button>
                    <button class="action-btn success" onmouseover="this.style.transform='translateY(-1px)'">悬停效果</button>
                    <button class="action-btn warning" disabled>禁用状态</button>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 16px; background: #f7fafc; border-radius: 6px;">
            <h3 style="margin: 0 0 12px 0; color: #2d3748; font-size: 16px;">布局规范说明：</h3>
            <ul style="margin: 0; padding-left: 20px; color: #4a5568; font-size: 14px; line-height: 1.6;">
                <li>采用2行3列的网格布局，确保按钮排列整齐</li>
                <li>统一按钮高度为38px，提升点击体验</li>
                <li>按钮间距为8px，保持视觉平衡</li>
                <li>添加轻微圆角(4px)和过渡动画，提升现代感</li>
                <li>悬停时有上移效果和阴影，增强交互反馈</li>
                <li>响应式设计，在不同屏幕尺寸下保持3列布局</li>
                <li>颜色系统规范化，每种类型按钮有对应的主题色</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 简单的交互演示
        document.getElementById('headlessBtn').addEventListener('click', function() {
            this.classList.toggle('primary');
            this.textContent = this.classList.contains('primary') ? 'Headless ✓' : 'Headless';
        });
        
        document.getElementById('minimizeBtn').addEventListener('click', function() {
            this.classList.toggle('success');
            this.textContent = this.classList.contains('success') ? 'Minimize ✓' : 'Minimize';
        });
    </script>
</body>
</html>
