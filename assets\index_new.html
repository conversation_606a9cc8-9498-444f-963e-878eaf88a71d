<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>CPL</title>
    <link rel="stylesheet" href="style.css?v=20250127">
</head>
<body>
    <div class="app-container">
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧面板 - 配置文件列表 -->
            <div class="left-panel">
                <div class="panel-header">
                    <div class="panel-title">配置文件</div>
                    <div class="panel-controls">
                        <button class="control-btn" onclick="selectAll()">全选</button>
                        <button class="control-btn" onclick="deselectAll()">清空</button>
                        <button class="control-btn" onclick="refreshProfiles()">刷新</button>
                    </div>
                </div>
                <div class="profile-list">
                    <div class="profile-grid" id="profileGrid">
                        <!-- 配置文件网格将动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 操作区域 -->
            <div class="right-panel">
                <div class="action-area">
                    <!-- 批量操作区域 - 按照button_layout_test.html规范的2行3列布局 -->
                    <div class="action-section">
                        <div class="section-title">
                            <span>批量操作</span>
                            <span id="selectionCount">已选择: 0</span>
                        </div>
                        <!-- 第一行：核心功能按钮 -->
                        <div class="action-grid">
                            <button class="action-btn primary" onclick="launchBingRewards()">必应奖励</button>
                            <button class="action-btn success" onclick="readRecords()">读取记录</button>
                            <button class="action-btn warning" onclick="launchBilibiliSearch()">哔哩搜索</button>
                        </div>
                        <!-- 第二行：辅助功能按钮 -->
                        <div class="action-grid">
                            <button class="action-btn info" onclick="autoMode()">自动模式</button>
                            <button class="action-btn cache" onclick="clearTodayCache()">清除缓存</button>
                            <button class="action-btn error" onclick="closeAllChrome()">关闭所有</button>
                        </div>
                    </div>

                    <!-- 选项区域 - 按照button_layout_test.html规范的3列布局 -->
                    <div class="action-section">
                        <div class="section-title">
                            <span>选项</span>
                        </div>
                        <div class="action-grid">
                            <button class="action-btn" id="headlessBtn" onclick="toggleHeadlessMode()">Headless</button>
                            <button class="action-btn" id="minimizeBtn" onclick="toggleMinimizeMode()">Minimize</button>
                            <button class="action-btn" style="opacity: 0.3; cursor: default;"></button>
                        </div>
                    </div>
                </div>

                <!-- 日志面板 -->
                <div class="log-panel">
                    <div class="log-header">
                        <div class="log-title">操作日志</div>
                        <div class="log-controls">
                            <button class="control-btn" onclick="clearLog()">清空</button>
                            <button class="control-btn" onclick="saveLog()">保存</button>
                        </div>
                    </div>
                    <div class="log-content" id="logContent">
                        <!-- 日志内容将动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 结果面板 -->
    <div class="results-panel" id="resultsPanel" style="display: none;">
        <div class="results-header">
            <h3>签到记录</h3>
            <button class="control-btn" onclick="closeResults()">关闭</button>
        </div>
        <div class="results-content" id="resultsContent">
            <!-- 结果内容将动态加载 -->
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <div class="loading-text">正在处理...</div>
        </div>
    </div>

    <script src="../src/renderer.js"></script>
</body>
</html>
