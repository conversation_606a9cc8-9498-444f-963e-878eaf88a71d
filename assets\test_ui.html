<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPL</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧面板 - 配置文件列表 -->
            <div class="left-panel">
                <div class="panel-header">
                    <div class="panel-title">配置文件</div>
                    <div class="panel-controls">
                        <button class="control-btn">全选</button>
                        <button class="control-btn">清空</button>
                        <button class="control-btn">刷新</button>
                    </div>
                </div>
                <div class="profile-list">
                    <div class="profile-grid" id="profileGrid">
                        <!-- 示例配置文件 -->
                        <div class="profile-item">
                            <div class="profile-number">1</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">2</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">3</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">4</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">5</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">6</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">7</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">8</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">9</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">10</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">11</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">12</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">13</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">14</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">15</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">16</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">17</div>
                        </div>
                        <div class="profile-item">
                            <div class="profile-number">18</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 操作区域 -->
            <div class="right-panel">
                <div class="action-area">
                    <!-- 批量操作区域 -->
                    <div class="action-section">
                        <div class="section-title">
                            <span>批量操作</span>
                            <span id="selectionCount">已选择: 0</span>
                        </div>
                        <!-- 第一行：核心功能按钮 -->
                        <div class="action-grid">
                            <button class="action-btn primary">必应奖励</button>
                            <button class="action-btn success">读取记录</button>
                            <button class="action-btn warning">哔哩搜索</button>
                        </div>
                        <!-- 第二行：辅助功能按钮 -->
                        <div class="action-grid">
                            <button class="action-btn info">自动模式</button>
                            <button class="action-btn cache">清除缓存</button>
                            <button class="action-btn error">关闭所有</button>
                        </div>
                    </div>

                    <!-- 选项区域 -->
                    <div class="action-section">
                        <div class="section-title">
                            <span>选项</span>
                        </div>
                        <div class="options-section">
                            <div class="option-item">
                                <div class="option-checkbox" id="headlessCheckbox" onclick="toggleHeadlessMode()">Headless</div>
                            </div>
                            <div class="option-item">
                                <div class="option-checkbox" id="minimizeCheckbox" onclick="toggleMinimizeMode()">Minimize</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志面板 - 占据50%空间 -->
                <div class="log-panel">
                    <div class="log-header">
                        <div class="log-title">操作日志</div>
                        <div class="log-controls">
                            <button class="control-btn">清空</button>
                            <button class="control-btn">保存</button>
                        </div>
                    </div>
                    <div class="log-content" id="logContent">
                        <div class="log-item info">
                            <div class="log-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="log-message">正在检测Chrome配置文件...</div>
                        </div>
                        <div class="log-item success">
                            <div class="log-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="log-message">检测完成，共发现 17 个配置文件</div>
                        </div>
                        <div class="log-item warning">
                            <div class="log-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div class="log-message">配置文件检测失败，使用默认配置</div>
                        </div>
                        <div class="log-item error">
                            <div class="log-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="log-message">配置文件检测出错: 无法访问Chrome目录</div>
                        </div>
                        <div class="log-item info">
                            <div class="log-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="log-message">正在启动Chrome配置文件...</div>
                        </div>
                        <div class="log-item success">
                            <div class="log-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="log-message">Chrome配置文件启动成功</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
