#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取Chrome配置文件列表的Python脚本
"""

import os
import json
import sys

def detect_chrome_profiles():
    """检测Chrome配置文件"""
    try:
        user_data_paths = [
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data"),
            os.path.expanduser(r"~\AppData\Local\Google\Chrome Beta\User Data"),
            os.path.expanduser(r"~\AppData\Local\Google\Chrome Dev\User Data"),
            os.path.expanduser(r"~\AppData\Local\Google\Chrome Canary\User Data")
        ]

        profiles = []
        # 只检查主要的Chrome版本
        main_chrome_path = os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data")

        if os.path.exists(main_chrome_path):
            # 检查Default配置文件
            default_path = os.path.join(main_chrome_path, "Default")
            if os.path.exists(default_path):
                preferences_file = os.path.join(default_path, "Preferences")
                if os.path.exists(preferences_file):
                    profiles.append("Default")

            # 检查所有Profile配置文件
            try:
                for item in os.listdir(main_chrome_path):
                    item_path = os.path.join(main_chrome_path, item)
                    if os.path.isdir(item_path) and item.startswith("Profile "):
                        preferences_file = os.path.join(item_path, "Preferences")
                        if os.path.exists(preferences_file):
                            profiles.append(item)
            except PermissionError:
                pass  # 忽略权限错误，继续处理已找到的配置文件

        # 去重并排序
        profiles = list(set(profiles))
        
        def natural_sort_key(profile):
            if profile == "Default":
                return (0, 0)
            elif profile.startswith("Profile "):
                try:
                    num = int(profile.split(" ")[1])
                    return (1, num)
                except (IndexError, ValueError):
                    return (2, profile)
            else:
                return (2, profile)

        profiles.sort(key=natural_sort_key)
        
        # 转换为显示格式
        result = []
        for profile in profiles:
            if profile == "Default":
                display_name = "1"
            elif profile.startswith("Profile "):
                try:
                    profile_num = int(profile.split(" ")[1])
                    display_name = str(profile_num + 1)
                except (IndexError, ValueError):
                    display_name = profile
            else:
                display_name = profile
                
            result.append({
                'real_name': profile,
                'display_name': display_name
            })
        
        return result

    except Exception as e:
        return [{'real_name': 'Default', 'display_name': '1'}]

if __name__ == "__main__":
    profiles = detect_chrome_profiles()
    print(json.dumps(profiles, ensure_ascii=False))
