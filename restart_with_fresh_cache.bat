@echo off
chcp 437 >nul
echo Restarting CPL with fresh cache...
echo.

REM Kill any existing electron processes
echo Closing existing CPL instances...
taskkill /f /im electron.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Clear node_modules cache if needed
if exist "node_modules\.cache" (
    echo Clearing node cache...
    rmdir /s /q "node_modules\.cache" >nul 2>&1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Starting CPL with fresh cache...
echo CSS Version: 2024072701 (Normalized Button Layout)
echo.

REM Start with cache disabled
node_modules\.bin\electron.cmd . --min-width --disable-http-cache --disable-gpu-sandbox
if errorlevel 1 (
    echo Application failed to start
    pause
    exit /b 1
)

echo Application has exited.
pause
