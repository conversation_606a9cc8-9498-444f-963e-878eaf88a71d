<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPL</title>
    <style>
        /* 浅色主题配色方案 */
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #fefefe;
            --bg-tertiary: #f5f7ff;
            --titlebar-bg: #f8f9ff;
            --border: #e8ebf7;
            --border-light: #f0f2ff;
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --accent: #667eea;
            --accent-hover: #5a67d8;
            --success: #48bb78;
            --warning: #ed8936;
            --error: #f56565;
            --info: #4299e1;
            --button-bg: #667eea;
            --button-hover: #5a67d8;
            --input-bg: #ffffff;
            --input-border: #e2e8f0;
            --scrollbar: #e2e8f0;
            --scrollbar-hover: #cbd5e0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow: hidden;
            user-select: none;
            font-size: 13px;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: var(--bg-primary);
        }



        /* 主要内容区域 - 简化布局 */
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
            gap: 1px;
            background: var(--border);
        }

        /* 左侧面板 - 配置文件列表 */
        .left-panel {
            width: 50%;
            background: var(--bg-secondary);
            display: flex;
            flex-direction: column;
        }

        /* 右侧面板 - 操作和日志区 */
        .right-panel {
            flex: 1;
            background: var(--bg-primary);
            display: flex;
            flex-direction: column;
        }

        /* 面板头部 */
        .panel-header {
            height: 40px;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
        }

        .panel-title {
            font-size: 13px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .panel-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            padding: 4px 8px;
            border: 1px solid var(--border);
            background: var(--bg-primary);
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 4px;
            font-size: 11px;
        }

        .control-btn:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border-color: var(--accent);
        }

        /* 配置文件列表 - 紧凑网格布局 */
        .profile-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .profile-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(32px, 1fr));
            gap: 6px;
            padding: 8px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .profile-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            user-select: none;
            background: var(--bg-tertiary);
            border: 2px solid transparent;
            position: relative;
            margin: 0 auto;
        }

        .profile-item:hover {
            background: var(--accent);
            color: white;
        }

        .profile-item.selected {
            background: var(--accent);
            color: white;
            border-color: var(--accent);
        }

        .profile-number {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-primary);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            line-height: 1;
        }

        .profile-item:hover .profile-number,
        .profile-item.selected .profile-number {
            color: white;
        }



        /* 操作区域 - 窗口化紧凑布局 */
        .action-area {
            padding: 6px 8px;
            background: var(--bg-primary);
            flex-shrink: 0;
            max-height: 140px;
        }

        .action-section {
            margin-bottom: 4px;
        }

        .section-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        /* 操作按钮网格 - 1行3列布局 */
        .action-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 6px;
            margin-bottom: 6px;
        }

        .action-btn {
            height: 28px;
            border: 1px solid var(--border);
            background: var(--bg-primary);
            color: var(--text-primary);
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 500;
        }

        .action-btn:hover {
            background: var(--bg-secondary);
            border-color: var(--accent);
        }

        .action-btn.primary {
            background: #e3f2fd;
            border-color: #90caf9;
            color: #1976d2;
        }

        .action-btn.primary:hover {
            background: #bbdefb;
            border-color: #64b5f6;
        }

        .action-btn.success {
            background: #e8f5e8;
            border-color: #a5d6a7;
            color: #388e3c;
        }

        .action-btn.success:hover {
            background: #c8e6c9;
            border-color: #81c784;
        }

        .action-btn.warning {
            background: #fce4ec;
            border-color: #f8bbd9;
            color: #c2185b;
        }

        .action-btn.warning:hover {
            background: #f8bbd9;
            border-color: #f48fb1;
        }

        .action-btn.error {
            background: #ffebee;
            border-color: #ffcdd2;
            color: #d32f2f;
        }

        .action-btn.error:hover {
            background: #ffcdd2;
            border-color: #ef9a9a;
        }

        .action-btn.info {
            background: #e0f7fa;
            border-color: #80deea;
            color: #0097a7;
        }

        .action-btn.info:hover {
            background: #b2ebf2;
            border-color: #4dd0e1;
        }

        /* 选项区域 - 窗口化布局 */
        .options-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 4px;
            padding: 8px;
        }

        .option-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            user-select: none;
        }

        .option-checkbox {
            width: 16px;
            height: 16px;
            border: 1px solid var(--border);
            border-radius: 2px;
            background: var(--input-bg);
            position: relative;
            cursor: pointer;
        }

        .option-checkbox.checked {
            background: var(--accent);
            border-color: var(--accent);
        }

        .option-checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .option-label {
            font-size: 12px;
            color: var(--text-primary);
        }

        /* 日志区域 - 窗口化布局，占据主要空间 */
        .log-panel {
            flex: 1;
            background: var(--bg-secondary);
            border-top: 1px solid var(--border);
            display: flex;
            flex-direction: column;
            min-height: 200px;
            overflow: hidden;
        }

        .log-header {
            height: 32px;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
            flex-shrink: 0;
        }

        .log-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .log-controls {
            display: flex;
            gap: 6px;
        }

        .log-content {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.4;
            background: var(--bg-primary);
        }

        .log-item {
            display: flex;
            margin-bottom: 3px;
            padding: 2px 0;
        }

        .log-message {
            color: var(--text-primary);
            flex: 1;
        }

        .log-item.success .log-message {
            color: var(--success);
        }

        .log-item.warning .log-message {
            color: var(--warning);
        }

        .log-item.error .log-message {
            color: var(--error);
        }

        .log-item.info .log-message {
            color: var(--info);
        }

        /* 滚动条样式 - 浅色风格 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--scrollbar);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-hover);
        }

        ::-webkit-scrollbar-corner {
            background: var(--bg-secondary);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .left-panel {
                width: 45%;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .left-panel {
                width: 100%;
                height: 250px;
            }

            .action-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .profile-grid {
                grid-template-columns: repeat(8, 1fr);
                gap: 6px;
            }

            .profile-item {
                width: 28px;
                height: 28px;
            }

            .profile-number {
                font-size: 14px;
            }
        }

        /* 小窗口优化 */
        @media (max-width: 600px) {
            .left-panel {
                height: 200px;
            }

            .profile-grid {
                grid-template-columns: repeat(6, 1fr);
                gap: 4px;
            }

            .profile-item {
                width: 24px;
                height: 24px;
            }

            .profile-number {
                font-size: 13px;
            }
        }

        /* 加载状态 */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

    </style>
</head>
<body>
    <div class="app-container">

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧面板 - 配置文件列表 -->
            <div class="left-panel">
                <div class="panel-header">
                    <div class="panel-title">配置文件</div>
                    <div class="panel-controls">
                        <button class="control-btn" onclick="selectAll()">全选</button>
                        <button class="control-btn" onclick="deselectAll()">清空</button>
                        <button class="control-btn" onclick="refreshProfiles()">刷新</button>
                    </div>
                </div>
                <div class="profile-list">
                    <div class="profile-grid" id="profileGrid">
                        <!-- 配置文件网格将动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 操作区域 -->
            <div class="right-panel">
                <div class="action-area">
                    <!-- 批量操作区域 -->
                    <div class="action-section">
                        <div class="section-title">
                            <span>批量操作</span>
                            <span id="selectionCount">已选择: 0</span>
                        </div>
                        <!-- 第一行：核心功能按钮 -->
                        <div class="action-grid">
                            <button class="action-btn primary" onclick="launchBingRewards()">必应奖励</button>
                            <button class="action-btn success" onclick="readRecords()">读取记录</button>
                            <button class="action-btn warning" onclick="launchBilibiliSearch()">哔哩搜索</button>
                        </div>
                        <!-- 第二行：辅助功能按钮 -->
                        <div class="action-grid">
                            <button class="action-btn info" onclick="autoMode()">自动模式</button>
                            <button class="action-btn cache" onclick="clearTodayCache()">清除缓存</button>
                            <button class="action-btn error" onclick="closeAllChrome()">关闭所有</button>
                        </div>
                    </div>

                    <!-- 选项区域 -->
                    <div class="action-section">
                        <div class="section-title">
                            <span>选项</span>
                        </div>
                        <div class="options-section">
                            <div class="option-item">
                                <div class="option-checkbox" id="headlessCheckbox" onclick="toggleHeadlessMode()">Headless</div>
                            </div>
                            <div class="option-item">
                                <div class="option-checkbox" id="minimizeCheckbox" onclick="toggleMinimizeMode()">Minimize</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志面板 -->
                <div class="log-panel">
                    <div class="log-header">
                        <div class="log-title">操作日志</div>
                        <div class="log-controls">
                            <button class="control-btn" onclick="clearLog()">清空</button>
                            <button class="control-btn" onclick="saveLog()">保存</button>
                        </div>
                    </div>
                    <div class="log-content" id="logContent">
                        <!-- 日志内容将动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../src/renderer.js"></script>
</body>
</html>