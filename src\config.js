// CPL 配置文件
const path = require('path');
const os = require('os');

class Config {
    // 路径配置
    static DEFAULT_CHROME_PATH = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe";
    static STALK_DIR = "C:\\Users\\<USER>\\Downloads\\RTBS";

    // URL配置
    static BILIBILI_SEARCH_URL = "https://www.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW";
    static AUTO_SEARCH_URL = "https://cn.bing.com/search?q=%E4%BD%A0%E5%B9%B2%E5%98%9B%E5%93%8E%E5%93%9F&qs=n&form=QBRE&sp=-1&lq=0&pq=%E4%BD%A0%E5%B9%B2%E5%98%9B%E5%93%8E%E5%93%9F&sc=12-5&sk=&cvid=0AC6297DC24E403BAD29E6EADD6B1153";
    static BING_REWARDS_URL = "https://rewards.bing.com";

    // 时间配置
    static BROWSER_START_DELAY = 3000;
    static PAGE_LOAD_DELAY = 3000;
    static PROCESS_END_DELAY = 5000;
    static AUTO_READ_DELAY = 10000;
    static HEADLESS_CLEANUP_DELAY = 60000;
    static AUTO_MODE_PROFILE_DELAY = 10000;
    static BILIBILI_PROFILE_DELAY = 5000;

    // 并发配置
    static MAX_BING_WORKERS = 4;
    static MAX_AUTO_WORKERS = 2;
    static MAX_BILIBILI_WORKERS = 3;

    // UI配置
    static WINDOW_SIZE = { width: 1200, height: 800 };
    static MIN_SIZE = { width: 800, height: 600 };

    // 获取用户特定的Stalk目录
    static getUserStalkDir() {
        const userProfile = os.userInfo().homedir;
        return path.join(userProfile, 'Downloads', 'RTBS');
    }

    // 获取Chrome用户数据目录
    static getChromeUserDataPaths() {
        const userProfile = os.userInfo().homedir;
        return [
            path.join(userProfile, 'AppData', 'Local', 'Google', 'Chrome', 'User Data'),
            path.join(userProfile, 'AppData', 'Local', 'Google', 'Chrome Beta', 'User Data'),
            path.join(userProfile, 'AppData', 'Local', 'Google', 'Chrome Dev', 'User Data'),
            path.join(userProfile, 'AppData', 'Local', 'Google', 'Chrome Canary', 'User Data')
        ];
    }

    // 获取Chrome无头模式启动参数
    static getChromeHeadlessArgs(tempDir, downloadDir) {
        return [
            `--user-data-dir=${tempDir}`,
            '--headless=new',
            '--disable-gpu',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-extensions',
            '--disable-plugins',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            `--download-directory=${downloadDir}`,
            '--enable-automation',
            '--disable-blink-features=AutomationControlled',
            '--remote-debugging-port=0',
            '--disable-background-networking',
            '--disable-sync',
            '--metrics-recording-only',
            '--no-report-upload',
            '--safebrowsing-disable-auto-update'
        ];
    }

    // 获取URL显示名称
    static getUrlDisplayName(url) {
        if (url.includes('bing.com/search') && url.includes('%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9')) {
            return '哔哩搜索';
        } else if (url.includes('cn.bing.com/search') && url.includes('%E4%BD%A0%E5%B9%B2%E5%98%9B%E5%93%8E%E5%93%9F')) {
            return '你干嘛哎哟搜索';
        } else if (url.includes('rewards.bing.com')) {
            return '必应奖励';
        } else {
            return '网页';
        }
    }

    // 配置文件名称转换
    static getProfileDisplayName(profileName) {
        if (profileName === 'Default') {
            return '1';
        } else if (profileName.startsWith('Profile ')) {
            try {
                const profileNum = parseInt(profileName.split(' ')[1]);
                return String(profileNum + 1);
            } catch (error) {
                return profileName;
            }
        } else {
            return profileName;
        }
    }

    static getProfileRealName(displayName) {
        try {
            const num = parseInt(displayName);
            if (num === 1) {
                return 'Default';
            } else {
                return `Profile ${num - 1}`;
            }
        } catch (error) {
            return displayName;
        }
    }

    // 文件夹管理方法
    static getTodayFolderName() {
        const today = new Date();
        return `${today.getMonth() + 1}月${today.getDate()}日`;
    }

    static getTodayFolderPath() {
        const stalkDir = this.getUserStalkDir();
        const todayFolder = this.getTodayFolderName();
        return path.join(stalkDir, todayFolder);
    }

    static getYesterdayFolderName() {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return `${yesterday.getMonth() + 1}月${yesterday.getDate()}日`;
    }

    static getYesterdayFolderPath() {
        const stalkDir = this.getUserStalkDir();
        const yesterdayFolder = this.getYesterdayFolderName();
        return path.join(stalkDir, yesterdayFolder);
    }
}

module.exports = Config;
