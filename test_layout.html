<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置文件布局测试</title>
    <style>
        /* 浅色主题配色方案 */
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --border: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --accent: #0d6efd;
            --input-bg: #ffffff;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-secondary);
        }

        .test-container {
            max-width: 300px;
            background: var(--bg-primary);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 16px;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border);
            margin-bottom: 12px;
        }

        .panel-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .panel-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            padding: 4px 8px;
            border: 1px solid var(--border);
            background: var(--bg-primary);
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 4px;
            font-size: 11px;
            transition: all 0.1s ease;
        }

        .control-btn:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border-color: var(--accent);
        }

        /* 配置文件网格布局 - 圆形数字，5列布局 */
        .profile-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
            padding: 12px;
        }

        .profile-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
            background: var(--bg-tertiary);
            border: 2px solid transparent;
            position: relative;
            margin: 0 auto;
        }

        .profile-item:hover {
            background: var(--accent);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
        }

        .profile-item.selected {
            background: var(--accent);
            color: white;
            border-color: var(--accent);
            box-shadow: 0 4px 12px rgba(13, 110, 253, 0.4);
            transform: scale(1.05);
        }

        .profile-number {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-primary);
            transition: color 0.2s ease;
        }

        .profile-item:hover .profile-number,
        .profile-item.selected .profile-number {
            color: white;
        }

        /* 状态指示器 */
        .profile-item::before {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: transparent;
            border-radius: 1px;
            transition: all 0.2s ease;
        }

        .profile-item.selected::before {
            background: rgba(255, 255, 255, 0.8);
        }

        .profile-item:hover::before {
            background: var(--accent);
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="panel-header">
            <div class="panel-title">配置文件</div>
            <div class="panel-controls">
                <button class="control-btn" onclick="selectAll()">全选</button>
                <button class="control-btn" onclick="deselectAll()">清空</button>
                <button class="control-btn" onclick="refreshProfiles()">刷新</button>
            </div>
        </div>
        <div class="profile-grid" id="profileGrid">
            <!-- 生成测试配置文件 -->
        </div>
    </div>

    <script>
        // 生成测试配置文件
        function generateTestProfiles() {
            const profileGrid = document.getElementById('profileGrid');
            profileGrid.innerHTML = '';
            
            for (let i = 1; i <= 17; i++) {
                const profileItem = document.createElement('div');
                profileItem.className = 'profile-item';
                profileItem.dataset.profile = i;
                
                profileItem.innerHTML = `
                    <div class="profile-number">${i}</div>
                `;

                profileItem.onclick = () => toggleProfile(i, profileItem);
                profileGrid.appendChild(profileItem);
            }
        }

        let selectedProfiles = new Set();

        function toggleProfile(profileName, profileItem) {
            if (selectedProfiles.has(profileName)) {
                selectedProfiles.delete(profileName);
                profileItem.classList.remove('selected');
            } else {
                selectedProfiles.add(profileName);
                profileItem.classList.add('selected');
            }
        }

        function selectAll() {
            selectedProfiles.clear();
            for (let i = 1; i <= 17; i++) {
                selectedProfiles.add(i);
            }
            updateSelection();
        }

        function deselectAll() {
            selectedProfiles.clear();
            updateSelection();
        }

        function updateSelection() {
            document.querySelectorAll('.profile-item').forEach(item => {
                const profileName = parseInt(item.dataset.profile);

                if (selectedProfiles.has(profileName)) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        function refreshProfiles() {
            generateTestProfiles();
            selectedProfiles.clear();
        }

        // 初始化
        generateTestProfiles();
    </script>
</body>
</html>
