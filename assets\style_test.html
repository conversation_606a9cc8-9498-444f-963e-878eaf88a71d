<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>样式测试 - CPL</title>
    <link rel="stylesheet" href="style.css?v=2024072701">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .version-info {
            background: #e3f2fd;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #1976d2;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background: #4caf50; }
        .status-error { background: #f44336; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">CPL 样式加载测试</h1>
        
        <div class="version-info">
            <strong>样式版本:</strong> 2024072701 (规范化按钮布局版本)<br>
            <strong>测试时间:</strong> <span id="testTime"></span>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3>样式加载状态检查:</h3>
            <div id="styleStatus"></div>
        </div>
        
        <!-- 复制主程序的按钮布局进行测试 -->
        <div class="action-section">
            <div class="section-title">
                <span>批量操作</span>
                <span>已选择: 0</span>
            </div>
            <!-- 第一行：核心功能按钮 -->
            <div class="action-grid">
                <button class="action-btn primary">必应奖励</button>
                <button class="action-btn success">读取记录</button>
                <button class="action-btn warning">哔哩搜索</button>
            </div>
            <!-- 第二行：辅助功能按钮 -->
            <div class="action-grid">
                <button class="action-btn info">自动模式</button>
                <button class="action-btn cache">清除缓存</button>
                <button class="action-btn error">关闭所有</button>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 16px; background: #f7fafc; border-radius: 6px;">
            <h3 style="margin: 0 0 12px 0; color: #2d3748; font-size: 16px;">检查项目：</h3>
            <ul style="margin: 0; padding-left: 20px; color: #4a5568; font-size: 14px; line-height: 1.6;">
                <li>按钮是否为2行3列布局</li>
                <li>按钮高度是否为38px</li>
                <li>按钮间距是否为8px</li>
                <li>按钮是否有4px圆角</li>
                <li>悬停时是否有上移效果和阴影</li>
                <li>颜色是否符合新的主题系统</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <button onclick="location.reload()" style="padding: 8px 16px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">
                强制刷新页面
            </button>
        </div>
    </div>
    
    <script>
        // 显示测试时间
        document.getElementById('testTime').textContent = new Date().toLocaleString();
        
        // 检查样式是否正确加载
        function checkStyles() {
            const statusDiv = document.getElementById('styleStatus');
            const testBtn = document.querySelector('.action-btn.primary');
            
            if (!testBtn) {
                statusDiv.innerHTML = '<span class="status-indicator status-error"></span>按钮元素未找到';
                return;
            }
            
            const styles = window.getComputedStyle(testBtn);
            const height = styles.height;
            const borderRadius = styles.borderRadius;
            const transition = styles.transition;
            
            let status = [];
            
            // 检查高度
            if (height === '38px') {
                status.push('<span class="status-indicator status-ok"></span>按钮高度: 38px ✓');
            } else {
                status.push('<span class="status-indicator status-error"></span>按钮高度: ' + height + ' (应为38px)');
            }
            
            // 检查圆角
            if (borderRadius === '4px') {
                status.push('<span class="status-indicator status-ok"></span>圆角: 4px ✓');
            } else {
                status.push('<span class="status-indicator status-error"></span>圆角: ' + borderRadius + ' (应为4px)');
            }
            
            // 检查过渡动画
            if (transition.includes('all') && transition.includes('0.2s')) {
                status.push('<span class="status-indicator status-ok"></span>过渡动画: 已启用 ✓');
            } else {
                status.push('<span class="status-indicator status-error"></span>过渡动画: 未检测到');
            }
            
            statusDiv.innerHTML = status.join('<br>');
        }
        
        // 页面加载完成后检查样式
        window.addEventListener('load', checkStyles);
        
        // 添加按钮交互测试
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('按钮点击测试: ' + this.textContent);
            });
        });
    </script>
</body>
</html>
