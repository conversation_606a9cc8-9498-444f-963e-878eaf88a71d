<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ffffff;
            color: #212529;
            padding: 20px;
        }
        
        .profile-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
            padding: 12px;
            max-width: 300px;
        }

        .profile-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            user-select: none;
            background: #e9ecef;
            border: 2px solid transparent;
            position: relative;
            margin: 0 auto;
        }

        .profile-item:hover {
            background: #0d6efd;
            color: white;
        }

        .profile-item.selected {
            background: #0d6efd;
            color: white;
            border-color: #0d6efd;
        }

        .profile-number {
            font-size: 14px;
            font-weight: bold;
            color: #212529;
            line-height: 1;
            text-align: center;
        }

        .profile-item:hover .profile-number,
        .profile-item.selected .profile-number {
            color: white;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 8px;
            max-width: 300px;
        }

        .action-btn {
            height: 36px;
            border: 1px solid #dee2e6;
            background: #ffffff;
            color: #212529;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .action-btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .action-btn.primary {
            background: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .action-btn.success {
            background: #198754;
            border-color: #198754;
            color: white;
        }

        .action-btn.warning {
            background: #fd7e14;
            border-color: #fd7e14;
            color: white;
        }

        .action-btn.info {
            background: #0dcaf0;
            border-color: #0dcaf0;
            color: #212529;
        }
        
        .option-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            user-select: none;
            margin: 10px 0;
        }

        .option-checkbox {
            width: 16px;
            height: 16px;
            border: 1px solid #dee2e6;
            border-radius: 2px;
            background: #ffffff;
            position: relative;
            cursor: pointer;
        }

        .option-checkbox.checked {
            background: #0d6efd;
            border-color: #0d6efd;
        }

        .option-checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .log-item {
            display: flex;
            margin-bottom: 3px;
            padding: 2px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .log-message {
            color: #212529;
            flex: 1;
        }
    </style>
</head>
<body>
    <h2>UI 测试页面</h2>
    
    <h3>1. 圆形按钮数字居中测试</h3>
    <div class="profile-grid">
        <div class="profile-item">
            <div class="profile-number">1</div>
        </div>
        <div class="profile-item">
            <div class="profile-number">2</div>
        </div>
        <div class="profile-item selected">
            <div class="profile-number">3</div>
        </div>
        <div class="profile-item">
            <div class="profile-number">10</div>
        </div>
        <div class="profile-item">
            <div class="profile-number">15</div>
        </div>
    </div>
    
    <h3>2. 按钮布局测试（一行两个）</h3>
    <div class="action-grid">
        <button class="action-btn primary">必应奖励</button>
        <button class="action-btn success">读取记录</button>
    </div>
    <div class="action-grid">
        <button class="action-btn warning">哔哩搜索</button>
        <button class="action-btn info">自动模式</button>
    </div>
    <div class="action-grid">
        <button class="action-btn">清除缓存</button>
        <button class="action-btn">关闭所有</button>
    </div>
    
    <h3>3. 无头模式选项测试</h3>
    <div class="option-item" onclick="toggleHeadless()">
        <div class="option-checkbox" id="headlessTest"></div>
        <div class="option-label">Headless Mode</div>
    </div>
    
    <h3>4. 日志显示测试（无时间）</h3>
    <div id="logTest">
        <div class="log-item">
            <span class="log-message">正在检测Chrome配置文件...</span>
        </div>
        <div class="log-item">
            <span class="log-message">检测完成，共发现 17 个配置文件</span>
        </div>
        <div class="log-item">
            <span class="log-message">Headless Mode enabled</span>
        </div>
    </div>
    
    <script>
        function toggleHeadless() {
            const checkbox = document.getElementById('headlessTest');
            checkbox.classList.toggle('checked');
        }
    </script>
</body>
</html>
